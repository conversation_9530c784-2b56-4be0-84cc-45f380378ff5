import { test, expect } from '@playwright/test';
import fs from 'fs';
import path from 'path';

const passwordFile = path.resolve(__dirname, 'currentPassword.txt');
const passwordA = 'password12';
const passwordB = 'password123';

// Read current password from file (or fallback to passwordA)
function getCurrentPassword(): string {
  try {
    return fs.readFileSync(passwordFile, 'utf-8').trim();
  } catch {
    return passwordA; // default on first run
  }
}

// Toggle between the two passwords
function getNewPassword(current: string): string {
  return current === passwordA ? passwordB : passwordA;
}

// Save new password to file
function savePassword(newPassword: string) {
  fs.writeFileSync(passwordFile, newPassword, 'utf-8');
}

test('change password and toggle', async ({ page }) => {
  const currentPassword = getCurrentPassword();
  const newPassword = getNewPassword(currentPassword);

  await page.goto('https://dev.pivotol.ai/login');

  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').fill(currentPassword);
  await page.getByRole('button', { name: 'Log in' }).click();

  await page.getByRole('button', { name: 'S', exact: true }).click();
  await page.waitForTimeout(3000);
  await page.getByText('User Preferences').click();
  await page.waitForTimeout(3000);
  await page.getByLabel('Current Password *').fill(currentPassword);
  await page.getByLabel('New Password *').fill(newPassword);
  await page.getByLabel('Confirm Password *').fill(newPassword);
  //await page.getByRole('button', { name: 'Submit' }).click();
  await page.click('button.MuiButton-containedPrimary.MuiButton-sizeLarge');
  await page.waitForTimeout(2000);
  await page.close();

  savePassword(newPassword); // save the new password for next run
});

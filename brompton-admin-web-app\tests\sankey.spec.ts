import { test, expect } from '@playwright/test';

test('drag and configure Sankey Chart widget', async ({ page }) => {
  await page.goto('https://dev.pivotol.ai/login');

  // **Login**
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000);

  // **Navigate to Dashboard**
  await page.getByRole('button', { name: 'Dashboards' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage' }).click();
  await page.waitForTimeout(10000);

  //await page.getByRole('button', { name: 'Proceed' }).click();
  await page.getByRole('button', { name: 'New Dashboard' }).click();
 

  // Open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(4000);

  // Click on the "Chart" option
  await page.locator('span.MuiTypography-root', { hasText: 'Chart' }).click();

  // Ensure the "Trend" element is visible
  await page.waitForSelector('text=Sankey', { timeout: 10000 });

  // Drag and drop the "Trend" widget
  const trendWidget = page.getByText('Sankey', { exact: true });
  const dropTarget = page.locator('.react-grid-layout.layout'); // Replace with the actual drop zone selector
  await trendWidget.dragTo(dropTarget);
  await page.waitForTimeout(2000);

  // Re-open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  // Hover over the parent element to reveal options
  const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
  await parentElement.hover();

  // Locate and click the 'MoreVertIcon' options button
  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  // Click on settings menu item
  await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[3]');
  await page.waitForTimeout(6000);

  await page.getByRole('button', { name: 'Add Label' }).click();
  await page.locator('#asset-autocomplete-0').click();
  await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();
  await page.getByLabel('Measurement').click();
  await page.getByRole('option', { name: 'Apparentenergy', exact: true }).click();
  await page.getByRole('button', { name: 'Add Label' }).click();
  await page.locator('#asset-autocomplete-1').click();
  await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();
  await page.getByLabel('Measurement').nth(1).click();
  await page.getByRole('option', { name: 'Averagevoltage', exact: true }).click();
  await page.getByRole('button', { name: 'Add Label' }).click();
  await page.locator('div').filter({ hasText: /^MeasureVirtualAdd LabelDelete$/ }).getByLabel('Virtual').check();
  await page.getByRole('textbox').nth(2).click();
  await page.getByRole('textbox').nth(2).fill('prime');
  await page.getByRole('button', { name: 'Add Label' }).click();
  await page.locator('div').filter({ hasText: /^MeasureVirtualAdd LabelDelete$/ }).getByLabel('Virtual').check();
  await page.getByRole('textbox').nth(3).click();
  await page.getByRole('textbox').nth(3).fill('gift');
  await page.getByRole('button', { name: 'Add Connection' }).click();
  await page.locator('#source-select').click();
  await page.getByRole('option', { name: 'Apparentenergy' }).click();
  await page.getByLabel('', { exact: true }).click();
  await page.getByRole('option', { name: 'prime' }).click();
  await page.getByRole('button', { name: 'Add Connection' }).click();
  await page.getByLabel('Apparentenergy').nth(1).click();
  await page.getByRole('option', { name: 'Averagevoltage' }).click();
  await page.getByLabel('prime').nth(1).click();
  await page.getByRole('option', { name: 'gift' }).click();
  await page.getByRole('button', { name: 'Add Connection' }).click();
  await page.getByLabel('Apparentenergy').nth(2).click();
  await page.getByRole('option', { name: 'Apparentenergy' }).click();
  await page.getByLabel('prime').nth(2).click();
  await page.getByRole('option', { name: 'prime' }).click();
  // **Click the Update button**
  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForTimeout(5000);

  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: 'Last 90 days' }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.waitForTimeout(5000);
  // **Close Page**
  await page.close();
});

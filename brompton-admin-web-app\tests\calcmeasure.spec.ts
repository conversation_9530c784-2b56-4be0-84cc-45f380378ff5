import { test, expect } from '@playwright/test';
test('calcmeasure', async({page}) => {
      // opening the URL
    await page.goto('https://dev.pivotol.ai/login')  // 60 seconds
    // Go to the username  and password
   await page.getByLabel('Username *').click();
   await page.getByLabel('Username *').fill('test');
   await page.getByLabel('Password *').click();
   await page.getByLabel('Password *').fill('Br0mpt0n!0T');
   // click on Login Button  
   await page.click('#__next > div > div > form > div > button');
   await page.waitForTimeout(3000);

     // Navigate to Assets -> Manage Assets
 await page.getByRole('button', { name: 'Assets' }).click();
 await page.waitForTimeout(3000);
 await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
 await page.waitForTimeout(10000);
 // Ensure the asset tree is loaded before proceeding
 //await page.waitForSelector('.MuiTreeItem-content', { timeout: 10000 });

 await page.locator('div.MuiBox-root.css-c36nl0:has-text("AssetAllFlows")').click({ button: 'right' });
 await page.waitForTimeout(5000);

 await page.getByRole('menuitem', { name: 'New measurement' }).click();
 await page.waitForTimeout(3000);
   
   //create new measurement
   await page.getByLabel('Tag *').click();
   await page.getByLabel('Tag *').fill('test create measure');
   await page.getByLabel('Description').click();
   await page.getByLabel('Description').fill('test');
   await page.getByLabel('Select measurement type *').click();
   await page.getByRole('option', { name: 'Acceleration', exact: true }).click();
   await page.getByLabel('Select data type *').click();
   await page.getByRole('option', { name: 'INT', exact: true }).click();
   await page.getByLabel('Select value type *').click();
   await page.getByRole('option', { name: 'count', exact: true }).click();
   await page.getByLabel('Select unit of measure').click();
   await page.getByRole('option', { name: 'ft/s²', exact: true }).click();
   await page.getByLabel('Select location').click();
   await page.getByRole('option', { name: 'Bottom', exact: true }).click();
   await page.getByLabel('Select datasource').click();
   await page.getByRole('option', { name: 'Calculation', exact: true }).click();
   await page.getByLabel('Meter factor').click();
   await page.getByLabel('Meter factor').fill('0');
   await page.waitForTimeout(2000);
   
   // click on next button
 
   await page.locator('button.MuiButton-containedPrimary.MuiButton-sizeLarge').click();
  // fill form
 await page.getByRole('combobox').first().click();
 await page.getByRole('option', { name: 'Euler Distance (2D)' }).click();
 await page.getByLabel('Select Measure').click();
 await page.locator('#measurement-list-option-0').click();
  await page.getByRole('button', { name: 'Open' }).nth(1).click();
  await page.getByRole('option', { name: 'Animesh\\WindTurbine\\Hydraulic' }).click();
  await page.getByRole('textbox', { name: 'Variable $A add comments' }).click();
  await page.getByRole('textbox', { name: 'Variable $A add comments' }).fill('asd');
  await page.getByRole('textbox', { name: 'Variable $B add comments' }).click();
  await page.getByRole('textbox', { name: 'Variable $B add comments' }).fill('sdf');
  await page.getByRole('checkbox', { name: 'Persistance' }).check();
  await page.getByRole('combobox').nth(3).click();
  await page.getByRole('option', { name: '10min' }).click();
  await page.getByRole('checkbox', { name: 'writeback' }).check();
  await page.getByRole('button', { name: 'Submit' }).click();
 await page.close();
   });
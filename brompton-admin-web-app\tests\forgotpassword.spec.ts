import { test, expect } from '@playwright/test';
test('forgetpassword', async({page}) => {
    // opening the URL
    await page.goto('https://dev.pivotol.ai/login');  // 60 seconds
   // Go to the username  and password
   await page.locator('//*[@id="__next"]/div/div/form/div/a').click();
   await page.getByLabel('Username or Email').click();
   await page.getByLabel('Username or Email').fill('<EMAIL>');
   await page.waitForTimeout(2000);
   //await page.getByLabel('Password *').click();
   //await page.getByLabel('Password *').fill('password123');
  // click on Login Button  
  await page.getByText('SUBMIT').click();
  await page.waitForTimeout(1000);
});

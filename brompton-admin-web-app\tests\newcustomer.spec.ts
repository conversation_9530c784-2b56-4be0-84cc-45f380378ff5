import { test, expect } from '@playwright/test';
test('createcustomer', async({page}) => {
        // Open Login Page
  await page.goto('https://dev.pivotol.ai/login');

  // Login Flow
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000); // Wait for login to complete

  // Navigate through dashboard
  await page.getByRole('button', { name: 'D', exact: true }).click();

  // Ensure "Customer Management" is loaded before clicking
  await page.waitForSelector('text=Customer Management', { timeout: 10000 });
  await page.getByText('Customer Management').click();

  // **Fix: Handle the "Proceed" button conditionally**
  const proceedButton = page.locator('button:has-text("Proceed")');
  const isProceedVisible = await proceedButton.isVisible();

  if (isProceedVisible) {
    console.log('Proceed button is visible, clicking it.');
    await proceedButton.click();
  } else {
    console.log('Proceed button is NOT visible, skipping this step.');
  }

  // **Fix: Click on the "Add new Customer" button**
  const addCustomerButton = page.locator('button.MuiButtonBase-root:has-text("Add new Customer")');

  await addCustomerButton.waitFor({ state: 'visible', timeout: 10000 });
  await addCustomerButton.click();

  // Fill Customer Details
  await page.getByLabel('Name id *').fill('sam');
  await page.getByLabel('Name *').fill('sam');
  await page.getByLabel('Address *').fill('neartree');

  // **Fix: Ensure File Upload Works Correctly**
  const fileInput = page.locator('input[type="file"]');
  await fileInput.setInputFiles('C:/Users/<USER>/OneDrive/Pictures/logo/sm_5afa55dd1cd4e.jpg');

  // Click Add
  await page.getByRole('button', { name: 'Add' }).click();

  // Close Browser
  await page.close();
});
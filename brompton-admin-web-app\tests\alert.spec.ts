import { test, expect } from '@playwright/test';

// Reusable configuration variables
const config = {
  url: 'https://dev.pivotol.ai/login',
  username: 'test',
  password: 'Br0mpt0n!0T',
  assetSearch: 'mqtt',
  assetName: 'Brenes > MAINPANEL_MQTT',
  measurementName: 'Brenes\\MAINPANEL_MQTT\\AverageCurrent',
  alertDescription: 'alert test',
  aggregateOption: 'AVG',
  aggregatePeriod: '10min',
  thresholdType: 'NOMINAL',
  comparisonCondition: 'LT',
  thresholdValue: '12',
  resetDeadbandValue: '1',
  userNotification: 'normaltest',
};

test('create alert for a measurement', async ({ page }) => {
  // Step 1: Login
  await page.goto(config.url);
  await page.getByLabel('Username *').fill(config.username);
  await page.getByLabel('Password *').fill(config.password);
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000);

  // Step 2: Navigate to Alerts > Manage
  await page.getByRole('button', { name: 'Alerts' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage' }).click();
  await page.waitForTimeout(5000);

  // Step 3: Create New Alert
  await page.getByRole('button', { name: 'Add New' }).click();

  await page.getByLabel('Asset').click();
  await page.getByRole('combobox', { name: 'Asset' }).fill(config.assetSearch);
  await page.getByRole('option', { name: config.assetName, exact: true }).click();

  await page.getByLabel('Measurement', { exact: true }).click();
  await page.getByRole('option', { name: config.measurementName, exact: true }).click();

  await page.getByLabel('Description', { exact: true }).fill(config.alertDescription);

  await page.getByLabel('Aggregate *').click();
  await page.getByRole('option', { name: config.aggregateOption, exact: true }).click();

  await page.getByLabel('Aggregate Period').click();
  await page.getByRole('option', { name: config.aggregatePeriod }).click();

  await page.getByLabel('Threshold Type').click();
  await page.getByRole('option', { name: config.thresholdType }).click();

  await page.getByLabel('Comparision condition').click();
  await page.getByRole('option', { name: config.comparisonCondition }).click();

  await page.getByLabel('Threshold Value').fill(config.thresholdValue);
  await page.getByLabel('Reset Deadband Value').fill(config.resetDeadbandValue);

  await page.getByLabel('User').click();
  await page.getByRole('option', { name: config.userNotification }).click();

  await page.getByLabel('Email').check();

  // Step 4: Submit the Alert
  await page.getByRole('button', { name: 'Submit' }).click();

  // Step 5: Close the Page
  await page.close();
});

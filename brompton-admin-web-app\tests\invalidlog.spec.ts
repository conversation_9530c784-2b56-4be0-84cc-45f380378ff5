import { test, expect } from '@playwright/test';

test('Negative Login Test - Invalid Credentials', async ({ page }) => {
  // Variables
  const baseUrl = 'https://dev.pivotol.ai/login';
  const invalidUsername = 'testhj';
  const invalidPassword = 'Br0mpt0n!0fdhfdT';
  const expectedUrlAfterFailure = baseUrl;

  console.log('Starting Negative Login Test');

  // Step 1: Go to login page
  await page.goto(baseUrl);
  console.log(`Navigated to: ${baseUrl}`);

  // Step 2: Fill in invalid credentials
  await page.getByLabel('Username *').fill(invalidUsername);
  await page.getByLabel('Password *').fill(invalidPassword);
  console.log('Filled in invalid credentials');

  // Step 3: Click on Login
  await page.getByRole('button', { name: 'Log in' }).click();
  console.log('Clicked login button');

  // Step 4: Wait for potential error message or same page to reload
  await page.waitForTimeout(3000);

  // Step 5: Assert URL has not changed
  const currentUrl = page.url();
  expect(currentUrl).toBe(expectedUrlAfterFailure);
  console.log('Assertion Passed: User remained on login page');

  // Step 6: Assert the error message is visible (this will FAIL if not found)
  const errorLocator = page.locator('text=Invalid credentials');
  await expect(errorLocator).toBeVisible({ timeout: 3000 }); // This makes it fail if not found
  console.log('Assertion Passed: Error message is visible');

  // Optional: Screenshot
  await page.screenshot({ path: 'negative-login-test.png' });

  console.log('Negative Login Test completed');
});

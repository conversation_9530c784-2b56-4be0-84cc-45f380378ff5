// npm test -- --testPathPattern=WidgetDataSettingsTabContainer.test.tsx --verbose --watchAll=false
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import React from 'react';
import { Provider } from 'react-redux';

import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import WidgetDataSettingsTabContainer from '../WidgetDataSettingsTabContainer';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock role permission hook
jest.mock('~/hooks/useRolePermission', () => ({
  useRolePermission: jest.fn(() => ({
    hasDashboardPermission: jest.fn(() => true),
  })),
  Role: {
    POWER_USER: 'POWER_USER',
  },
}));

// Mock API hooks
jest.mock('~/redux/api/assetsApi', () => ({
  useGetAllAssetQuery: jest.fn(() => ({
    data: [
      { id: 1, tag: 'Asset 1', assetTypeId: 1 },
      { id: 2, tag: 'Asset 2', assetTypeId: 2 },
    ],
    isFetching: false,
  })),
  useGetAllBackOfficeAssetTypesQuery: jest.fn(() => ({
    data: [
      { id: 1, name: 'Type 1' },
      { id: 2, name: 'Type 2' },
    ],
    isLoading: false,
    isSuccess: true,
  })),
}));

jest.mock('~/redux/api/measuresApi', () => ({
  useGetAllMeasurementsQuery: jest.fn(() => ({
    data: [
      { id: 1, tag: 'Measurement 1', metric_id: 1, unit: 'Unit 1' },
      { id: 2, tag: 'Measurement 2', metric_id: 2, unit: 'Unit 2' },
    ],
    isLoading: false,
    isSuccess: true,
  })),
  useGetMeasurementsByAssetIdQuery: jest.fn(() => ({
    data: [
      { id: 1, tag: 'Asset Measurement 1', metric_id: 1, unit: 'Unit 1' },
      { id: 2, tag: 'Asset Measurement 2', metric_id: 2, unit: 'Unit 2' },
    ],
    isLoading: false,
    isSuccess: true,
  })),
}));

// Mock utils
jest.mock('~/utils/utils', () => ({
  assetsPathMapper: jest.fn((data) =>
    data.map((item: any) => ({ id: item.id, label: item.tag, value: item.id })),
  ),
  getPreviousDate: jest.fn(() => new Date().getTime()),
  measurementsPathMapper: jest.fn((data) =>
    data.map((item: any) => ({ id: item.id, label: item.tag, value: item.id, unit: item.unit })),
  ),
}));

// Mock router object
const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: { customerId: '1', dashboardId: '1' },
  asPath: '',
  route: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
  basePath: '',
};

// Helper function to create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        currentDashboardId: 1,
        customer: {
          id: 1,
          name: 'Test Customer',
          nameId: 'test-customer',
          address: 'Test Address',
          enabled: true,
          logo: '',
        },
        ...initialState,
      },
    },
  });
};

const mockTheme = createTheme();

// Helper function to render component with providers
const renderWithProviders = (
  component: React.ReactElement,
  { store = createTestStore(), ...renderOptions } = {},
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>{children}</ThemeProvider>
    </Provider>
  );

  return {
    store,
    ...render(component, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Default props for the component
const defaultSettings = {
  assetMeasure: [],
  selectedTitles: [],
  dbMeasureIdToName: {},
  assetOrAssetType: null,
  timeRange: 6,
  samplePeriod: 2,
  globalSamplePeriod: false,
  overrideDateSettings: false,
  startDate: new Date(),
  endDate: new Date(),
  assetTz: true,
  aggBy: 1,
};

const defaultProps = {
  settings: defaultSettings,
  setSettings: jest.fn(),
  mode: 'dashboard' as const,
};

describe('WidgetDataSettingsTabContainer Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByText('Data Settings')).toBeInTheDocument();
    });

    it('should render asset selection in dashboard mode', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Select Asset')).toBeInTheDocument();
    });

    it('should render asset type selection in template mode', () => {
      const props = {
        ...defaultProps,
        mode: 'template' as const,
      };

      renderWithProviders(<WidgetDataSettingsTabContainer {...props} />);

      expect(screen.getByLabelText('Asset Type')).toBeInTheDocument();
    });

    it('should render measurements selection', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Select Measurements')).toBeInTheDocument();
    });

    it('should render time range settings', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should render sample period settings', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Sample Period')).toBeInTheDocument();
    });

    it('should render aggregation settings', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Aggregation')).toBeInTheDocument();
    });

    it('should render global sample period checkbox', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Use Global Sample Period')).toBeInTheDocument();
    });

    it('should render override date settings checkbox', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Override Date Settings')).toBeInTheDocument();
    });

    it('should render asset timezone checkbox', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Use Asset Timezone')).toBeInTheDocument();
    });

    it('should show date pickers when override date settings is enabled', () => {
      const settings = {
        ...defaultSettings,
        overrideDateSettings: true,
      };

      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} settings={settings} />);

      expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
      expect(screen.getByLabelText('End Date')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should handle asset selection in dashboard mode', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const assetField = screen.getByLabelText('Select Asset');
      fireEvent.mouseDown(assetField);

      await waitFor(() => {
        const option = screen.getByText('Asset 1');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle asset type selection in template mode', async () => {
      const setSettings = jest.fn();
      const props = {
        ...defaultProps,
        mode: 'template' as const,
        setSettings,
      };

      renderWithProviders(<WidgetDataSettingsTabContainer {...props} />);

      const assetTypeField = screen.getByLabelText('Asset Type');
      fireEvent.mouseDown(assetTypeField);

      await waitFor(() => {
        const option = screen.getByText('Type 1');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle measurements selection', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const measurementsField = screen.getByLabelText('Select Measurements');
      fireEvent.mouseDown(measurementsField);

      await waitFor(() => {
        const option = screen.getByText('Measurement 1');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle time range selection', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const timeRangeField = screen.getByLabelText('Time Range');
      fireEvent.mouseDown(timeRangeField);

      await waitFor(() => {
        const option = screen.getByText('Last Hour');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle sample period selection', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const samplePeriodField = screen.getByLabelText('Sample Period');
      fireEvent.mouseDown(samplePeriodField);

      await waitFor(() => {
        const option = screen.getByText('1 Second');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle aggregation selection', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const aggregationField = screen.getByLabelText('Aggregation');
      fireEvent.mouseDown(aggregationField);

      await waitFor(() => {
        const option = screen.getByText('Average');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle global sample period checkbox toggle', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const checkbox = screen.getByLabelText('Use Global Sample Period');
      await userEvent.click(checkbox);

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle override date settings checkbox toggle', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const checkbox = screen.getByLabelText('Override Date Settings');
      await userEvent.click(checkbox);

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle asset timezone checkbox toggle', async () => {
      const setSettings = jest.fn();
      renderWithProviders(
        <WidgetDataSettingsTabContainer {...defaultProps} setSettings={setSettings} />,
      );

      const checkbox = screen.getByLabelText('Use Asset Timezone');
      await userEvent.click(checkbox);

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle start date change', async () => {
      const setSettings = jest.fn();
      const settings = {
        ...defaultSettings,
        overrideDateSettings: true,
      };

      renderWithProviders(
        <WidgetDataSettingsTabContainer
          {...defaultProps}
          settings={settings}
          setSettings={setSettings}
        />,
      );

      const startDateField = screen.getByLabelText('Start Date');
      fireEvent.change(startDateField, { target: { value: '2023-01-01' } });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle end date change', async () => {
      const setSettings = jest.fn();
      const settings = {
        ...defaultSettings,
        overrideDateSettings: true,
      };

      renderWithProviders(
        <WidgetDataSettingsTabContainer
          {...defaultProps}
          settings={settings}
          setSettings={setSettings}
        />,
      );

      const endDateField = screen.getByLabelText('End Date');
      fireEvent.change(endDateField, { target: { value: '2023-12-31' } });

      expect(setSettings).toHaveBeenCalled();
    });
  });

  describe('Redux Integration', () => {
    it('should use customer data from Redux state', () => {
      const store = createTestStore();
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />, { store });

      const mockUseGetAllAssetQuery = require('~/redux/api/assetsApi').useGetAllAssetQuery;
      expect(mockUseGetAllAssetQuery).toHaveBeenCalledWith(1);
    });

    it('should handle missing customer data gracefully', () => {
      const store = createTestStore({ customer: null });

      expect(() => {
        renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />, { store });
      }).not.toThrow();
    });
  });

  describe('API Integration', () => {
    it('should fetch assets for dashboard mode', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      const mockUseGetAllAssetQuery = require('~/redux/api/assetsApi').useGetAllAssetQuery;
      expect(mockUseGetAllAssetQuery).toHaveBeenCalled();
    });

    it('should fetch asset types for template mode', () => {
      const props = {
        ...defaultProps,
        mode: 'template' as const,
      };

      renderWithProviders(<WidgetDataSettingsTabContainer {...props} />);

      const mockUseGetAllBackOfficeAssetTypesQuery =
        require('~/redux/api/assetsApi').useGetAllBackOfficeAssetTypesQuery;
      expect(mockUseGetAllBackOfficeAssetTypesQuery).toHaveBeenCalled();
    });

    it('should fetch measurements when asset is selected', () => {
      const settings = {
        ...defaultSettings,
        assetOrAssetType: 1,
      };

      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} settings={settings} />);

      const mockUseGetMeasurementsByAssetIdQuery =
        require('~/redux/api/measuresApi').useGetMeasurementsByAssetIdQuery;
      expect(mockUseGetMeasurementsByAssetIdQuery).toHaveBeenCalledWith(1);
    });

    it('should handle loading states for assets', () => {
      const mockUseGetAllAssetQuery = require('~/redux/api/assetsApi').useGetAllAssetQuery;
      mockUseGetAllAssetQuery.mockReturnValue({
        data: null,
        isFetching: true,
      });

      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should handle loading states for measurements', () => {
      const mockUseGetAllMeasurementsQuery =
        require('~/redux/api/measuresApi').useGetAllMeasurementsQuery;
      mockUseGetAllMeasurementsQuery.mockReturnValue({
        data: null,
        isLoading: true,
      });

      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should handle API errors gracefully', () => {
      const mockUseGetAllAssetQuery = require('~/redux/api/assetsApi').useGetAllAssetQuery;
      mockUseGetAllAssetQuery.mockReturnValue({
        data: null,
        isFetching: false,
        error: { message: 'API Error' },
      });

      expect(() => {
        renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle empty asset list', () => {
      const mockUseGetAllAssetQuery = require('~/redux/api/assetsApi').useGetAllAssetQuery;
      mockUseGetAllAssetQuery.mockReturnValue({
        data: [],
        isFetching: false,
      });

      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Select Asset')).toBeInTheDocument();
    });

    it('should handle empty measurements list', () => {
      const mockUseGetAllMeasurementsQuery =
        require('~/redux/api/measuresApi').useGetAllMeasurementsQuery;
      mockUseGetAllMeasurementsQuery.mockReturnValue({
        data: [],
        isLoading: false,
      });

      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Select Measurements')).toBeInTheDocument();
    });

    it('should handle invalid date inputs', async () => {
      const setSettings = jest.fn();
      const settings = {
        ...defaultSettings,
        overrideDateSettings: true,
      };

      renderWithProviders(
        <WidgetDataSettingsTabContainer
          {...defaultProps}
          settings={settings}
          setSettings={setSettings}
        />,
      );

      const startDateField = screen.getByLabelText('Start Date');
      fireEvent.change(startDateField, { target: { value: 'invalid-date' } });

      // Component should handle invalid date gracefully
      expect(() => {
        fireEvent.blur(startDateField);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for form controls', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      expect(screen.getByLabelText('Select Asset')).toBeInTheDocument();
      expect(screen.getByLabelText('Select Measurements')).toBeInTheDocument();
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
      expect(screen.getByLabelText('Sample Period')).toBeInTheDocument();
      expect(screen.getByLabelText('Aggregation')).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      const assetField = screen.getByLabelText('Select Asset');
      assetField.focus();

      expect(assetField).toHaveFocus();

      fireEvent.keyDown(assetField, { key: 'Tab' });

      const measurementsField = screen.getByLabelText('Select Measurements');
      expect(measurementsField).toBeInTheDocument();
    });

    it('should have proper form validation messages', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      // Check for required field indicators or validation messages
      const requiredFields = screen.getAllByRole('combobox');
      requiredFields.forEach((field) => {
        expect(field).toHaveAttribute('aria-required');
      });
    });

    it('should have proper fieldset and legend structure', () => {
      renderWithProviders(<WidgetDataSettingsTabContainer {...defaultProps} />);

      const fieldsets = screen.getAllByRole('group');
      expect(fieldsets.length).toBeGreaterThan(0);
    });
  });
});

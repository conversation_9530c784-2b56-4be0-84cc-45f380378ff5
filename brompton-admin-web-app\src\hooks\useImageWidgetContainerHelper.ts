import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { dashboardTemplateApi } from '~/redux/api/dashboardTemplate';
import { measuresApi, useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { DashboardState, TimeRangeOptions } from '~/types/dashboard';
import { ImageWidget } from '~/types/widgets';
import { assetsPathMapper, getPreviousDate } from '~/utils/utils';

type ImageWidgetContainerHelperProps = {
  settings: ImageWidget;
};
const useImageWidgetContainerHelper = ({ settings }: ImageWidgetContainerHelperProps) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const router = useRouter();
  const [openSnackBar, setOpenSnackBar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const activeCustomer = useSelector(getActiveCustomer);
  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );
  const assetTypesWithPathForAsset = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);
  const { data: assetMeasurements } = useGetAllMeasurementsQuery(
    {
      assetId: settings.assetOrAssetType ?? 0,
      customerId: activeCustomer?.id ?? 0,
    },
    {
      skip:
        settings.mode === 'template' ||
        settings.assetOrAssetType === null ||
        settings.assetOrAssetType <= 0 ||
        router.pathname === '/measurement-browser',
    },
  );
  const onImageDashboardLinkClick = async () => {
    const mode = settings.mode;
    const dashboard = settings.imgDashboard.dashboard;
    const dashboardOrTemplate = settings.imgDashboard.dashboardOrTemplate;
    // const dashboard = settings.dashboard;
    // const dashboardOrTemplate = settings.dashboardOrTemplate;
    if (mode === 'dashboard' && dashboard !== null) {
      // if (settings.openDashboardInNewTab) {
      //   window.open(
      //     `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id}`,
      //     '_blank',
      //   );
      //   return;
      // }
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        setOpenSnackBar({
          open: true,
          message: 'Fetching dashboard template data...',
          severity: 'info',
        });
        const {
          isError,
          error,
          data: templateData,
        } = await dispatch(
          dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(dashboard.id),
        );
        setOpenSnackBar({
          open: false,
          message: 'Dashboard template data fetched successfully!',
          severity: 'success',
        });
        const {
          isError: isErrorForMeasurements,
          error: errorForMeasurements,
          data: assetMeasurements,
        } = await dispatch(
          measuresApi.endpoints.getAllMeasurements.initiate({
            customerId: activeCustomer?.id ?? 0,
            assetId: settings.imgDashboard.assetOrAssetType ?? 0,
          }),
        );
        if ((isError && error) || (isErrorForMeasurements && errorForMeasurements)) {
          const err = error as CustomError;
          setOpenSnackBar({
            open: true,
            message: err.data.exception ?? 'Error fetching dashboard template. Please try again.',
            severity: 'error',
          });
        }
        if (templateData && templateData?.data && assetMeasurements) {
          const templateDetailsData = JSON.parse(templateData.data) as {
            widget: DashboardState['widget'];
            topPanel: DashboardState['template']['topPanel'];
            chart: DashboardState['template']['chart'];
            desktopMobile: DashboardState['desktopMobile'];
            responsiveLayouts: DashboardState['responsiveLayouts'];
          };
          const metricToMeasurementMap: Record<string, string[]> = {};
          assetMeasurements?.forEach((measurement) => {
            if (measurement.metric_id !== null) {
              const metricIdStr = measurement.metric_id.toString();
              const measurementIdStr = measurement.id.toString();

              if (!metricToMeasurementMap[metricIdStr]) {
                metricToMeasurementMap[metricIdStr] = [];
              }

              metricToMeasurementMap[metricIdStr].push(measurementIdStr);
            }
          });
          templateDetailsData.responsiveLayouts?.mobile?.widgets.map((widget) => {
            if (widget.type === 'chart') {
              widget.settings.settings.dashboardOrTemplate =
                widget.settings.settings.dashboardOrTemplate ?? 'template';
              widget.settings.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
              widget.settings.settings.mode = 'dashboard';
              if (
                'assetMeasure' in widget.settings.settings &&
                widget.settings.settings.assetMeasure
              ) {
                if (Array.isArray(widget.settings.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings.settings) {
                    widget.settings.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.settings.selectedTitles = [];
                  }
                  widget.settings.settings.assetMeasure.push({
                    assetId: settings.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings.settings &&
                    metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(
                      ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                    );
                    widget.settings.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.settings.assetMeasure.assetId =
                    settings.assetOrAssetType?.toString() ?? '';
                  widget.settings.settings.assetMeasure.measureId = measureIds;
                }
              }
              return widget;
            }

            widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
            widget.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
            widget.settings.mode = 'dashboard';
            if (widget.type === 'dashboard-widget') {
              const assetPath = assetTypesWithPathForAsset.find(
                (assetType) => assetType.value === settings.imgDashboard.assetOrAssetType,
              );
              widget.settings.assetOption = {
                id: settings.imgDashboard.assetOrAssetType ?? 0,
                label: assetPath?.label ?? '',
              };
            }
            if (widget.type === 'map') {
              widget.settings.markers = widget.settings.markers.map((marker) => {
                if (marker.selectedTitles.length > 0) {
                  const measureIds: string[] = [];
                  marker.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  measureIds.forEach((measureId) => {
                    marker.assetMeasures.push({
                      assetId: settings.assetOrAssetType?.toString() ?? '',
                      measureId: measureId,
                    });
                  });
                  let labelUnits = {};
                  measureIds.forEach((measureId) => {
                    const measure = assetMeasurements?.find(
                      (measure) => measure.id === Number(measureId),
                    );

                    labelUnits = {
                      ...labelUnits,
                      [measureId]: {
                        label: measure?.tag ?? '',
                        unit: '',
                        value: '',
                      },
                    };
                  });
                  marker.labelAndUnits = labelUnits;
                }
                marker.selectedTitles = [];
                return marker;
              });
            } else {
              if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
                if (Array.isArray(widget.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings) {
                    widget.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.selectedTitles = [];
                  }
                  if (widget.type === 'image') {
                    widget.settings.imgDashboard = {
                      ...widget.settings.imgDashboard,
                      assetOrAssetType: settings.imgDashboard.assetOrAssetType ?? null,
                      dashboard: settings.imgDashboard.dashboard,
                      dashboardOrTemplate: settings.imgDashboard.dashboardOrTemplate ?? 'dashboard',
                    };
                    widget.settings.assetOrAssetType =
                      settings.imgDashboard.assetOrAssetType ?? null;
                  }
                  widget.settings.assetMeasure.push({
                    assetId: settings.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings &&
                    metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                    widget.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.assetMeasure.assetId =
                    settings.assetOrAssetType?.toString() ?? '';
                  widget.settings.assetMeasure.measureId = measureIds;
                }
              }
            }
            if (widget.type === 'Diagram') {
              widget.settings.mode = 'dashboard';
              Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
                const variables = widget.settings.elementIdVariabels[elementId];

                variables.forEach((variable) => {
                  const measurementKey = variable.measurementId;
                  variable.aggBy = variable.aggBy ?? 1;
                  // Only replace if there's a mapping available
                  if (metricToMeasurementMap[measurementKey]) {
                    const mappedIds = metricToMeasurementMap[measurementKey];

                    // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                    variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                    variable.measurementId = mappedIds[0];

                    // Optional: clear label or other fields if needed
                    // variable.label = '';
                  }
                });
              });
              widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
                const measurementKey = variable.measurementId;
                // Only replace if there's a mapping available
                if (metricToMeasurementMap[measurementKey]) {
                  const mappedIds = metricToMeasurementMap[measurementKey];

                  // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                  variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  variable.measurementId = mappedIds[0];

                  // Optional: clear label or other fields if needed
                  // variable.label = '';
                }
                variable.aggBy = variable.aggBy ?? 1;
                return variable;
              });
            }
            return widget;
          });
          templateDetailsData.responsiveLayouts?.desktop?.widgets.map((widget) => {
            if (widget.type === 'chart') {
              widget.settings.settings.dashboardOrTemplate =
                widget.settings.settings.dashboardOrTemplate ?? 'template';
              widget.settings.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
              widget.settings.settings.mode = 'dashboard';
              if (
                'assetMeasure' in widget.settings.settings &&
                widget.settings.settings.assetMeasure
              ) {
                if (Array.isArray(widget.settings.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings.settings) {
                    widget.settings.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.settings.selectedTitles = [];
                  }
                  widget.settings.settings.assetMeasure.push({
                    assetId: settings.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings.settings &&
                    metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(
                      ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                    );
                    widget.settings.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.settings.assetMeasure.assetId =
                    settings.assetOrAssetType?.toString() ?? '';
                  widget.settings.settings.assetMeasure.measureId = measureIds;
                }
              }
              return widget;
            }

            widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
            widget.settings.assetOrAssetType = settings.assetOrAssetType ?? null;
            widget.settings.mode = 'dashboard';
            if (widget.type === 'dashboard-widget') {
              const assetPath = assetTypesWithPathForAsset.find(
                (assetType) => assetType.value === settings.imgDashboard.assetOrAssetType,
              );
              widget.settings.assetOption = {
                id: settings.imgDashboard.assetOrAssetType ?? 0,
                label: assetPath?.label ?? '',
              };
            }
            if (widget.type === 'map') {
              widget.settings.markers = widget.settings.markers.map((marker) => {
                if (marker.selectedTitles.length > 0) {
                  const measureIds: string[] = [];
                  marker.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  measureIds.forEach((measureId) => {
                    marker.assetMeasures.push({
                      assetId: settings.assetOrAssetType?.toString() ?? '',
                      measureId: measureId,
                    });
                  });
                  let labelUnits = {};
                  measureIds.forEach((measureId) => {
                    const measure = assetMeasurements?.find(
                      (measure) => measure.id === Number(measureId),
                    );

                    labelUnits = {
                      ...labelUnits,
                      [measureId]: {
                        label: measure?.tag ?? '',
                        unit: '',
                        value: '',
                      },
                    };
                  });
                  marker.labelAndUnits = labelUnits;
                }
                marker.selectedTitles = [];
                return marker;
              });
            } else {
              if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
                if (Array.isArray(widget.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings) {
                    widget.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.selectedTitles = [];
                  }
                  if (widget.type === 'image') {
                    widget.settings.imgDashboard = {
                      ...widget.settings.imgDashboard,
                      assetOrAssetType: settings.imgDashboard.assetOrAssetType ?? null,
                      dashboard: settings.imgDashboard.dashboard,
                      dashboardOrTemplate: settings.imgDashboard.dashboardOrTemplate ?? 'dashboard',
                    };
                    widget.settings.assetOrAssetType =
                      settings.imgDashboard.assetOrAssetType ?? null;
                  }
                  widget.settings.assetMeasure.push({
                    assetId: settings.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings &&
                    metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                    widget.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.assetMeasure.assetId =
                    settings.assetOrAssetType?.toString() ?? '';
                  widget.settings.assetMeasure.measureId = measureIds;
                }
              }
            }
            if (widget.type === 'Diagram') {
              widget.settings.mode = 'dashboard';
              Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
                const variables = widget.settings.elementIdVariabels[elementId];

                variables.forEach((variable) => {
                  const measurementKey = variable.measurementId;
                  variable.aggBy = variable.aggBy ?? 1;
                  // Only replace if there's a mapping available
                  if (metricToMeasurementMap[measurementKey]) {
                    const mappedIds = metricToMeasurementMap[measurementKey];

                    // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                    variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                    variable.measurementId = mappedIds[0];

                    // Optional: clear label or other fields if needed
                    // variable.label = '';
                  }
                });
              });
              widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
                const measurementKey = variable.measurementId;
                // Only replace if there's a mapping available
                if (metricToMeasurementMap[measurementKey]) {
                  const mappedIds = metricToMeasurementMap[measurementKey];

                  // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                  variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  variable.measurementId = mappedIds[0];

                  // Optional: clear label or other fields if needed
                  // variable.label = '';
                }
                variable.aggBy = variable.aggBy ?? 1;
                return variable;
              });
            }
            return widget;
          });
          dispatch(
            dashboardSlice.actions.setDashboardCrumb({
              dashboardId: -2,
              title: dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
              assetType: templateData?.asset_template?.assetType?.id ?? undefined,
              templateId: templateData?.id ?? undefined,
              assetId: settings.imgDashboard.assetOrAssetType ?? undefined,
            }),
          );
          router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
          dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
          dispatch(
            dashboardSlice.actions.setCurrentDashboardTitle(
              dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
            ),
          );
          const lastWidget = templateDetailsData.responsiveLayouts?.desktop?.widgets
            ?.sort((a, b) => Number(a.id) - Number(b.id))
            ?.at(-1);
          dispatch(
            dashboardSlice.actions.setWidget({
              widgets: templateDetailsData.responsiveLayouts?.desktop?.widgets ?? [],
              deleteWidgets: [],
              widgetLayout: templateDetailsData.responsiveLayouts?.desktop?.widgetLayout ?? [],
              lastWidgetId: Number(lastWidget?.id) ?? 0,
            }),
          );
          dispatch(
            dashboardSlice.actions.setWidgetsLayout(templateDetailsData.widget.widgetLayout),
          );
          dispatch(
            dashboardSlice.actions.setSamplePeriod(templateDetailsData.topPanel.samplePeriod),
          );
          dispatch(
            dashboardSlice.actions.setRefreshTimeInterval(
              templateDetailsData.topPanel.refreshInterval,
            ),
          );
          dispatch(
            dashboardSlice.actions.setTimeRangeType(templateDetailsData.topPanel.timeRangeType),
          );
          const minutes: number =
            TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6].serverValue;
          const start = getPreviousDate(minutes);
          if (templateDetailsData.topPanel.timeRangeType !== 0) {
            dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
            dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
          } else {
            dispatch(
              dashboardSlice.actions.setChartStartDate(
                new Date(templateDetailsData.chart.startDate),
              ),
            );
            dispatch(
              dashboardSlice.actions.setChartEndDate(new Date(templateDetailsData.chart.endDate)),
            );
          }
        }
      }
    }
    if (mode === 'template' && dashboard !== null) {
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        dispatch(dashboardSlice.actions.setTemplateId(dashboard.id));
        dispatch(dashboardSlice.actions.setTemplateName(dashboard.title));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: [],
            deleteWidgets: [],
            widgetLayout: [],
            lastWidgetId: 0,
          }),
        );
      }
    }
  };
  return {
    onImageDashboardLinkClick,
    openSnackBar,
    setOpenSnackBar,
    // onImageTextDashboardLinkClick,
  };
};

export default useImageWidgetContainerHelper;

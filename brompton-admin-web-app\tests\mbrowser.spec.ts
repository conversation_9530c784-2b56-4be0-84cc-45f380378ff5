import { test, expect } from '@playwright/test';

test('Measurement browser', async ({ page }) => {
  // Opening the login page
  await page.goto('https://dev.pivotol.ai/login');

  // Fill in Username and Password
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');

  // Click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000); // Wait for login to process
  await page.setViewportSize({ width: 1920, height: 1080 });
  // Navigate to Assets -> Manage Assets
  await page.getByRole('button', { name: 'Assets' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Measurement Browser' }).click();
  await page.waitForTimeout(10000);

  // Click the first "Trend" link (if multiple exist)
  await page.locator('[data-testid="InsertChartSharpIcon"]').first().click({ force: true });
  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: 'Last 90 days' }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.waitForTimeout(3000);
  // Close the page
  await page.close();
});

import { test, expect } from '@playwright/test';
test('assettemplate', async({page}) => {
        await page.goto('https://dev.pivotol.ai/login');
  
  // Login
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  
  await page.waitForTimeout(3000); // Reduce unnecessary delay

  // Click "Assets" button to expand menu
  await page.getByRole('button', { name: 'Assets' }).click();
  await page.waitForTimeout(5000); // Reduce unnecessary delay
  // Wait for "Manage Templates" menu item to appear and click
  const manageTemplates = page.getByRole('menuitem', { name: 'Manage Templates' });
  await manageTemplates.waitFor({ state: 'visible', timeout: 10000 });
  await manageTemplates.click();

    // Try different locators to ensure correct selection
  const addAssetTemplate = page.locator('a[href="/create-asset-template"]'); 
await addAssetTemplate.waitFor({ state: 'visible', timeout: 15000 });
await addAssetTemplate.click();


// Ensure dropdown options are visible
await page.waitForTimeout(5000); // Short delay to allow rendering
  // Fill in the form
  await page.getByRole('combobox', { name: 'Asset Type' }).click();
  await page.getByRole('option', { name: 'Power > AC Generator (19)', exact: true }).click();
  await page.getByLabel('Manufacturer').fill('Flower');
  await page.getByLabel('Model Number').fill('786786');
  await page.getByLabel('Save as Global Asset Template').check();
  await page.getByRole('button', { name: 'Next' }).click();

  // Submit Form
  await page.getByLabel('Type Id *').click();
  await page.getByRole('option', { name: 'Angular Position' }).click();
  await page.getByLabel('Data Type *').click();
  await page.getByRole('option', { name: 'BOOLEAN' }).click();
  await page.getByLabel('Select value type *').click();
  await page.getByRole('option', { name: 'calculated' }).click();
  await page.getByLabel('Metric *').click();
  await page.getByRole('option', { name: 'Voltagepower' }).click();
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('test');
  await page.getByLabel('Location').click();
  await page.getByRole('option', { name: 'Hot Side' }).click();
  await page.getByLabel('Data Source').click();
  await page.getByRole('option', { name: 'Weather' }).click();
  await page.getByLabel('Meter Factor').click();
  await page.getByLabel('Meter Factor').fill('3');
  await page.getByRole('button', { name: 'Add' }).click();
  await page.getByRole('button', { name: 'Save & Finish' }).click();
  await page.close();
});
import { test, expect } from '@playwright/test';
test('createasset', async({page}) => {
      // Opening the login page
  await page.goto('https://dev.pivotol.ai/login');

  // Fill in Username and Password
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');

  // Click on Login Button  
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(2000); // Wait for login to process

  // Navigate to Assets -> Manage Assets
  await page.locator('div.MuiListItemText-root span', { hasText: 'Assets' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
  await page.waitForTimeout(5000);

  // Ensure the asset tree is loaded before proceeding
  await page.waitForSelector('.MuiTreeItem-content', { timeout: 10000 });

  // Right-click on an existing asset entry
  const assetEntry = page.locator('.MuiTreeItem-content.Mui-expanded.Mui-selected');
  await assetEntry.click({ button: 'right' });
  //await page.waitForTimeout(5000);
  // Ensure the context menu appears
  await page.waitForSelector('.MuiList-root.MuiList-padding.MuiMenu-list', { timeout: 5000 });

  // Click on "New root asset" using text instead of dynamic class names
  const newRootAsset = page.getByRole('menuitem', { name: 'New root asset' });
  await newRootAsset.click();

  // Fill in asset details
  await page.getByLabel('Tag *').fill('TestAsset'); 

  await page.getByLabel('Select an asset type').click();
  await page.getByRole('option', { name: 'Motor > AC 3 Phase (0)', exact: true }).click();

  // Ensure dropdown is visible before selecting timezone
  //await page.getByTestId('ArrowDropDownIcon').nth(2).waitFor({ state: 'visible', timeout: 3000 });

  await page.getByLabel('Description').fill('TestAssets');

  await page.getByLabel('Select a time zone').click();
  await page.getByRole('option', { name: 'Africa/Abidjan', exact: true }).click();

  // Ensure dropdown is visible before selecting latitude/longitude
  await page.getByTestId('ArrowDropDownIcon').nth(1).waitFor({ state: 'visible', timeout: 2000 });

  await page.getByLabel('Latitude').fill('0');
  await page.getByLabel('Longitude').fill('0');

  // Submit the form
  await page.getByText('Submit').click();
  await page.waitForTimeout(2000); // Wait for submission process

  // Close the page
  await page.close();
});
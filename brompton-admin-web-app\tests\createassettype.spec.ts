import { test, expect } from '@playwright/test';
test('createassettype', async ({ page }) => {
        await page.goto('https://dev.pivotol.ai/login');

        // Login Flow
        await page.getByLabel('Username *').fill('test');
        await page.getByLabel('Password *').fill('Br0mpt0n!0T');
        await page.getByRole('button', { name: 'Log in' }).click();
        await page.waitForTimeout(3000); // Wait for login
      
        // Navigate to Asset Management
        await page.getByRole('button', { name: 'Add Dashboard' }).click();
        await page.getByRole('button', { name: 'Assets' }).click();
        await page.getByRole('menuitem', { name: 'Manage Types' }).click();
        await page.getByRole('button', { name: 'Add new Asset type' }).click();
      
        // Fill Asset Type Form
        await page.getByLabel('Asset Type Name').fill('test flow');
      
        // Open Parent Type Dropdown
        await page.getByLabel('Parent Type').click();
        await page.waitForTimeout(1000); // Ensure dropdown loads
      
        // **Fix: Select "Power > AC Generator" Correctly**
        const options = page.locator('li[role="option"]:has-text("Power > AC Generator")');
        
        if (await options.count() > 1) {
          console.log('Multiple options found, selecting the first one.');
          await options.first().click();
        } else {
          console.log('Only one matching option found, selecting it.');
          await options.click();
        }
      
        // Save Asset Type
        await page.getByRole('button', { name: 'Save' }).click();
        // Close the page
        await page.close();
      });
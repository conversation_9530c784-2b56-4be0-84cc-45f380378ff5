import { configureStore } from '@reduxjs/toolkit';
import { Layout } from 'react-grid-layout';
import { dashboardSlice } from '../dashboardSlice';
import { Customer } from '~/types/customers';
import { UserPreferences } from '~/types/userPreferences';
import { Widget } from '~/types/widgets';

type TestStore = ReturnType<
  typeof configureStore<{
    dashboard: ReturnType<typeof dashboardSlice.reducer>;
  }>
>;

describe('dashboardSlice', () => {
  let store: TestStore;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        dashboard: dashboardSlice.reducer,
      },
    });
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = store.getState().dashboard;

      expect(state.currentDashboardId).toBe(-1);
      expect(state.dashboardTitle).toBe('');
      expect(state.userDetails).toBeNull();
      expect(state.userToken).toBeNull();
      expect(state.customer).toBeNull();
      expect(state.enableZoom).toBe(false);
      expect(state.mainPanel).toBe('chart');
      expect(state.isLeftPanelOpen).toBe(true);
      expect(state.isDirty).toBe(true);
      expect(state.kisok).toBe(false);
      expect(state.fullScreen).toBe(false);
      expect(state.rightSideBar).toBe(false);
      expect(state.dateFormat).toBe(0);
      expect(state.desktopMobile).toBe(0);
      expect(state.responsiveLayouts).toEqual({
        desktop: { widgetLayout: [], widgets: [] },
        mobile: { widgetLayout: [], widgets: [] },
      });
      expect(state.widget.widgets).toEqual([]);
      expect(state.widget.widgetLayout).toEqual([]);
      expect(state.widget.deleteWidgets).toEqual([]);
      expect(state.widget.lastWidgetId).toBe(5);
    });
  });

  describe('Customer Actions', () => {
    const mockCustomer: Customer = {
      id: 1,
      name: 'Test Customer',
      nameId: 'test-customer',
      address: '123 Test Street',
      logo: 'test-logo.png',
      enabled: true,
    };

    it('should set active customer', () => {
      store.dispatch(dashboardSlice.actions.setActiveCustomer(mockCustomer));

      const state = store.getState().dashboard;
      expect(state.customer).toEqual(mockCustomer);
    });

    it('should reset dashboard when switching to different customer', () => {
      // Set initial customer
      store.dispatch(dashboardSlice.actions.setActiveCustomer(mockCustomer));

      // Add some widgets and set dashboard ID
      store.dispatch(dashboardSlice.actions.setCurrentDashboardId(123));
      store.dispatch(dashboardSlice.actions.selectMainPanel('chart'));

      // Switch to different customer
      const newCustomer: Customer = {
        ...mockCustomer,
        id: 2,
        nameId: 'different-customer',
      };
      store.dispatch(dashboardSlice.actions.setActiveCustomer(newCustomer));

      const state = store.getState().dashboard;
      expect(state.customer).toEqual(newCustomer);
      expect(state.currentDashboardId).toBe(-1);
      expect(state.mainPanel).toBe('chart');
      expect(state.widget.widgets).toEqual([]);
      expect(state.widget.widgetLayout).toEqual([]);
    });

    it('should not reset dashboard when setting same customer', () => {
      // Set initial customer and dashboard state
      store.dispatch(dashboardSlice.actions.setActiveCustomer(mockCustomer));
      store.dispatch(dashboardSlice.actions.setCurrentDashboardId(123));
      store.dispatch(dashboardSlice.actions.selectMainPanel('chart'));

      // Set same customer again
      store.dispatch(dashboardSlice.actions.setActiveCustomer(mockCustomer));

      const state = store.getState().dashboard;
      expect(state.currentDashboardId).toBe(123);
      expect(state.mainPanel).toBe('chart');
    });
  });

  describe('Dashboard State Actions', () => {
    it('should set current dashboard ID', () => {
      store.dispatch(dashboardSlice.actions.setCurrentDashboardId(42));

      const state = store.getState().dashboard;
      expect(state.currentDashboardId).toBe(42);
    });

    it('should set dashboard title', () => {
      const title = 'My Test Dashboard';
      store.dispatch(dashboardSlice.actions.setCurrentDashboardTitle(title));

      const state = store.getState().dashboard;
      expect(state.dashboardTitle).toBe(title);
    });

    it('should set main panel', () => {
      store.dispatch(dashboardSlice.actions.selectMainPanel('chart'));

      const state = store.getState().dashboard;
      expect(state.mainPanel).toBe('chart');
    });

    it('should set left panel open state', () => {
      store.dispatch(dashboardSlice.actions.setIsLeftPanelOpen(false));

      const state = store.getState().dashboard;
      expect(state.isLeftPanelOpen).toBe(false);
    });

    it('should set full screen mode', () => {
      store.dispatch(dashboardSlice.actions.setFullScreen(true));

      const state = store.getState().dashboard;
      expect(state.fullScreen).toBe(true);
    });

    it('should set right sidebar state', () => {
      store.dispatch(dashboardSlice.actions.setRightSideBar(true));

      const state = store.getState().dashboard;
      expect(state.rightSideBar).toBe(true);
    });

    it('should set widgets zoom', () => {
      store.dispatch(dashboardSlice.actions.setWidgetsZoom(true));

      const state = store.getState().dashboard;
      expect(state.enableZoom).toBe(true);
    });

    it('should set date format', () => {
      store.dispatch(dashboardSlice.actions.setDateFormat(1));

      const state = store.getState().dashboard;
      expect(state.dateFormat).toBe(1);
    });

    it('should set new measure ID', () => {
      store.dispatch(dashboardSlice.actions.setNewMeasureId(100));

      const state = store.getState().dashboard;
      expect(state.newMeasureId).toBe(100);
    });
  });

  describe('User Actions', () => {
    it('should set user token', () => {
      const token = 'test-token-123';
      store.dispatch(dashboardSlice.actions.setUserToken(token));

      const state = store.getState().dashboard;
      expect(state.userToken).toBe(token);
    });

    it('should reset user token', () => {
      store.dispatch(dashboardSlice.actions.setUserToken('test-token'));
      store.dispatch(dashboardSlice.actions.resetUserToken());

      const state = store.getState().dashboard;
      expect(state.userToken).toBeNull();
    });

    it('should set user preferences', () => {
      const preferences: UserPreferences = {
        DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
        DEFAULT_CUSTOMER: 'test-customer',
        THOUSAND_SEPARATOR: 'enabled',
      };

      store.dispatch(dashboardSlice.actions.setUserPreferences(preferences));

      const state = store.getState().dashboard;
      expect(state.userPreferences).toEqual(preferences);
    });

    it('should set right sidebar active tab', () => {
      store.dispatch(dashboardSlice.actions.setRightSideBarActiveTab('/icons/llm.svg'));

      const state = store.getState().dashboard;
      expect(state.rightSideBarActiveTab).toBe('/icons/llm.svg');
    });
  });

  describe('Tree Actions', () => {
    it('should set current selected node ID', () => {
      store.dispatch(dashboardSlice.actions.setCurrentSelectedNodeId('node-123'));

      const state = store.getState().dashboard;
      expect(state.tree.currentSelectedNodeId).toBe('node-123');
    });

    it('should set selected node IDs', () => {
      const nodeIds = ['node-1', 'node-2', 'node-3'];
      store.dispatch(dashboardSlice.actions.setSelectedNodeIds(nodeIds));

      const state = store.getState().dashboard;
      expect(state.tree.selectedNodeIds).toEqual(nodeIds);
    });

    it('should remove selected node ID', () => {
      const nodeIds = ['node-1', 'node-2', 'node-3'];
      store.dispatch(dashboardSlice.actions.setSelectedNodeIds(nodeIds));
      store.dispatch(dashboardSlice.actions.removeSelectedNodeId('node-2'));

      const state = store.getState().dashboard;
      expect(state.tree.selectedNodeIds).toEqual(['node-1', 'node-3']);
    });

    it('should set expanded node IDs and mark as dirty', () => {
      const nodeIds = ['node-1', 'node-2'];
      store.dispatch(dashboardSlice.actions.setExpandedNodeIds(nodeIds));

      const state = store.getState().dashboard;
      expect(state.tree.expandedNodeIds).toEqual(nodeIds);
      expect(state.isDirty).toBe(true);
    });

    it('should set selected view measure ID', () => {
      store.dispatch(dashboardSlice.actions.setSelectedViewMeasureId('measure-456'));

      const state = store.getState().dashboard;
      expect(state.tree.selectedViewMeasureId).toBe('measure-456');
    });
  });

  describe('Checkbox Actions', () => {
    it('should select checkbox and mark as dirty', () => {
      const payload = {
        assetId: 'asset-1',
        metricId: 'metric-1',
        metricName: 'Temperature',
      };

      store.dispatch(dashboardSlice.actions.selectCheckbox(payload));

      const state = store.getState().dashboard;
      expect(state.tree.dbMeasureIdToName['metric-1']).toBe('Temperature');
      expect(state.isDirty).toBe(true);
    });

    it('should unselect all checkboxes and mark as dirty', () => {
      // First select some checkboxes
      store.dispatch(
        dashboardSlice.actions.selectCheckbox({
          assetId: 'asset-1',
          metricId: 'metric-1',
          metricName: 'Temperature',
        }),
      );
      store.dispatch(
        dashboardSlice.actions.selectCheckbox({
          assetId: 'asset-2',
          metricId: 'metric-2',
          metricName: 'Pressure',
        }),
      );

      // Then unselect all
      store.dispatch(dashboardSlice.actions.unSelectAllCheckbox());

      const state = store.getState().dashboard;
      expect(state.tree.dbMeasureIdToName).toEqual({});
      expect(state.isDirty).toBe(true);
    });

    it('should unselect specific checkbox and mark as dirty', () => {
      // First select checkboxes
      store.dispatch(
        dashboardSlice.actions.selectCheckbox({
          assetId: 'asset-1',
          metricId: 'metric-1',
          metricName: 'Temperature',
        }),
      );
      store.dispatch(
        dashboardSlice.actions.selectCheckbox({
          assetId: 'asset-2',
          metricId: 'metric-2',
          metricName: 'Pressure',
        }),
      );

      // Unselect one
      store.dispatch(
        dashboardSlice.actions.unselectCheckbox({
          assetId: 'asset-1',
          metricId: 'metric-1',
        }),
      );

      const state = store.getState().dashboard;
      expect(state.tree.dbMeasureIdToName['metric-1']).toBeUndefined();
      expect(state.tree.dbMeasureIdToName['metric-2']).toBe('Pressure');
      expect(state.isDirty).toBe(true);
    });
  });

  describe('Widget Layout Actions', () => {
    const mockLayout: Layout[] = [
      { i: '1', x: 0, y: 0, w: 6, h: 4 },
      { i: '2', x: 6, y: 0, w: 6, h: 4 },
    ];

    it('should set widgets layout', () => {
      store.dispatch(dashboardSlice.actions.setWidgetsLayout(mockLayout));

      const state = store.getState().dashboard;
      expect(state.widget.widgetLayout).toEqual(mockLayout);
    });

    it('should update layout and mark as dirty', () => {
      const newLayout: Layout[] = [{ i: '1', x: 2, y: 2, w: 8, h: 6 }];

      store.dispatch(dashboardSlice.actions.updateLayout(newLayout));

      const state = store.getState().dashboard;
      expect(state.widget.widgetLayout).toEqual(newLayout);
      expect(state.isDirty).toBe(true);
    });

    it('should set static layout', () => {
      store.dispatch(dashboardSlice.actions.setWidgetsLayout(mockLayout));
      store.dispatch(dashboardSlice.actions.setStaticLayout('1'));

      const state = store.getState().dashboard;
      const widget = state.widget.widgetLayout.find((w) => w.i === '1');
      expect(widget?.static).toBe(true);
    });
  });

  describe('Responsive Layout Actions', () => {
    const desktopLayout: Layout[] = [
      { i: '1', x: 0, y: 0, w: 6, h: 4 },
      { i: '2', x: 6, y: 0, w: 6, h: 4 },
    ];

    const mobileLayout: Layout[] = [
      { i: '1', x: 0, y: 0, w: 12, h: 4 },
      { i: '2', x: 0, y: 4, w: 12, h: 4 },
    ];
    const desktopWidgets: Widget[] = [
      {
        id: '1',
        type: 'stats',
        settings: {
          title: { value: 'Desktop Stats', isVisible: true, color: '#000000' },
          isValid: true,
          isDirty: false,
          mode: 'dashboard' as const,
          isChildWidget: false,
          dashboardOrTemplate: 'dashboard' as const,
          assetOrAssetType: null,
          dashboard: null,
          openDashboardInNewTab: false,
          isRealTime: false,
          retainPeriod: 60,
          refreshInterval: -1,
          dbMeasureIdToName: {},
          fontSize: 12,
          fontWeight: 'bolder',
          selectedAssetId: '',
          assetMeasure: { assetId: '', measureId: [] },
          selectedDbMeasureId: '',
          showMin: true,
          showMinLable: true,
          minColor: '#000000',
          minBorder: true,
          min: { label: 'Min', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showMax: true,
          showMaxLabel: true,
          maxColor: '#000000',
          maxBorder: true,
          max: { label: 'Max', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showAvg: true,
          showAvgLabel: true,
          avgColor: '#000000',
          avgBorder: true,
          avg: { label: 'Avg', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showDelta: true,
          showDeltaLabel: true,
          deltaColor: '#000000',
          deltaBorder: true,
          delta: { label: 'Delta', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showSum: true,
          showSumLabel: true,
          sumColor: '#000000',
          sumBorder: true,
          sum: { label: 'Sum', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showTotal: true,
          showTotalLabel: true,
          totalColor: '#000000',
          totalBorder: true,
          total: { label: 'Total', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showCurrent: true,
          showCurrentLabel: true,
          currentColor: '#000000',
          showCurrentBorder: true,
          current: { label: 'Last Value', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          aggBy: 1,
          samplePeriod: 2,
          globalSamplePeriod: false,
          overrideAssetTz: false,
          overrideAssetTzValue: false,
          overrideGlobalSettings: false,
          isRelativeToGlboalEndTime: false,
          timeRange: 6,
          startDate: Date.now(),
          endDate: Date.now(),
        },
      },
    ];

    const mobileWidgets: Widget[] = [
      {
        id: '1',
        type: 'stats',
        settings: {
          title: { value: 'Mobile Stats', isVisible: true, color: '#000000' },
          isValid: true,
          isDirty: false,
          mode: 'dashboard' as const,
          isChildWidget: false,
          dashboardOrTemplate: 'dashboard' as const,
          assetOrAssetType: null,
          dashboard: null,
          openDashboardInNewTab: false,
          isRealTime: false,
          retainPeriod: 60,
          refreshInterval: -1,
          dbMeasureIdToName: {},
          fontSize: 12,
          fontWeight: 'bolder',
          selectedAssetId: '',
          assetMeasure: { assetId: '', measureId: [] },
          selectedDbMeasureId: '',
          showMin: true,
          showMinLable: true,
          minColor: '#000000',
          minBorder: true,
          min: { label: 'Min', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showMax: true,
          showMaxLabel: true,
          maxColor: '#000000',
          maxBorder: true,
          max: { label: 'Max', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showAvg: true,
          showAvgLabel: true,
          avgColor: '#000000',
          avgBorder: true,
          avg: { label: 'Avg', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showDelta: true,
          showDeltaLabel: true,
          deltaColor: '#000000',
          deltaBorder: true,
          delta: { label: 'Delta', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showSum: true,
          showSumLabel: true,
          sumColor: '#000000',
          sumBorder: true,
          sum: { label: 'Sum', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showTotal: true,
          showTotalLabel: true,
          totalColor: '#000000',
          totalBorder: true,
          total: { label: 'Total', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showCurrent: true,
          showCurrentLabel: true,
          currentColor: '#000000',
          showCurrentBorder: true,
          current: { label: 'Last Value', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          aggBy: 1,
          samplePeriod: 2,
          globalSamplePeriod: false,
          overrideAssetTz: false,
          overrideAssetTzValue: false,
          overrideGlobalSettings: false,
          isRelativeToGlboalEndTime: false,
          timeRange: 6,
          startDate: Date.now(),
          endDate: Date.now(),
        },
      },
    ];

    it('should set responsive layouts', () => {
      const responsiveLayouts = {
        desktop: { widgetLayout: desktopLayout, widgets: desktopWidgets },
        mobile: { widgetLayout: mobileLayout, widgets: mobileWidgets },
      };

      store.dispatch(dashboardSlice.actions.setResponsiveLayouts(responsiveLayouts));

      const state = store.getState().dashboard;
      expect(state.responsiveLayouts).toEqual(responsiveLayouts);
    });

    it('should switch from desktop to mobile mode', () => {
      // Set up initial state
      store.dispatch(dashboardSlice.actions.setWidgetsLayout(desktopLayout));
      store.dispatch(
        dashboardSlice.actions.setResponsiveLayouts({
          desktop: { widgetLayout: desktopLayout, widgets: desktopWidgets },
          mobile: { widgetLayout: mobileLayout, widgets: mobileWidgets },
        }),
      );

      // Switch to mobile
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));

      const state = store.getState().dashboard;
      expect(state.desktopMobile).toBe(1);
      expect(state.widget.widgetLayout).toEqual(mobileLayout);
      expect(state.responsiveLayouts?.desktop.widgetLayout).toEqual(desktopLayout);
    });

    it('should switch from mobile to desktop mode', () => {
      // Set up mobile state
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));
      store.dispatch(dashboardSlice.actions.setWidgetsLayout(mobileLayout));
      store.dispatch(
        dashboardSlice.actions.setResponsiveLayouts({
          desktop: { widgetLayout: desktopLayout, widgets: desktopWidgets },
          mobile: { widgetLayout: mobileLayout, widgets: mobileWidgets },
        }),
      );

      // Switch to desktop
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(0));

      const state = store.getState().dashboard;
      expect(state.desktopMobile).toBe(0);
      expect(state.widget.widgetLayout).toEqual(desktopLayout);
      expect(state.responsiveLayouts?.mobile.widgetLayout).toEqual(mobileLayout);
    });

    it('should update responsive layout when updating widget layout', () => {
      const newLayout: Layout[] = [{ i: '1', x: 3, y: 3, w: 6, h: 6 }];

      store.dispatch(
        dashboardSlice.actions.setResponsiveLayouts({
          desktop: { widgetLayout: desktopLayout, widgets: desktopWidgets },
          mobile: { widgetLayout: mobileLayout, widgets: mobileWidgets },
        }),
      );

      // Update layout while in desktop mode (default)
      store.dispatch(dashboardSlice.actions.updateLayout(newLayout));

      const state = store.getState().dashboard;
      expect(state.widget.widgetLayout).toEqual(newLayout);
      expect(state.responsiveLayouts?.desktop.widgetLayout).toEqual(newLayout);
      expect(state.responsiveLayouts?.mobile.widgetLayout).toEqual(mobileLayout);
    });
  });

  // it('should add chart widget', () => {
  //   const layout: Layout[] = [{ i: 'a', x: 0, y: 0, w: 2, h: 2 }];
  //   const layoutItem: Layout = { i: 'a', x: 0, y: 0, w: 2, h: 2 };

  //   store.dispatch(
  //     dashboardSlice.actions.addWidget({
  //       widgetMode: 'dashboard',
  //       type: 'chart',
  //       layout,
  //       layoutItem,
  //     }),
  //   );

  //   const state = store.getState().dashboard;

  //   expect(state.widget.widgets).toHaveLength(1); // ✅ FIXED: now expecting 1 widget
  //   expect(state.widget.widgets[0]).toBeDefined();
  //   expect(state.widget.widgets[0].type).toBe('chart');
  //   expect(state.widget.widgetLayout[0].w).toBe(6); // Default width for chart
  //   expect(state.widget.widgetLayout[0].h).toBe(10); // Default height for chart
  // });

  describe('Widget Actions', () => {
    let store: TestStore;

    beforeEach(() => {
      store = configureStore({
        reducer: {
          dashboard: dashboardSlice.reducer,
        },
      });
    });

    it('should add chart widget', () => {
      const layout: Layout[] = [{ i: 'a', x: 0, y: 0, w: 2, h: 2 }];
      const layoutItem: Layout = { i: 'a', x: 0, y: 0, w: 2, h: 2 };

      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'stats',
          layout,
          layoutItem,
        }),
      );

      const state = store.getState().dashboard;

      expect(state.widget.widgets).toHaveLength(1);
      expect(state.widget.widgets[0]).toBeDefined();
      expect(state.widget.widgets[0].type).toBe('stats');
      expect(state.widget.widgetLayout[0].w).toBe(6);
      expect(state.widget.widgetLayout[0].h).toBe(2);
    });
  });

  describe('Dashboard Crumb Actions', () => {
    it('should set dashboard crumb', () => {
      store.dispatch(dashboardSlice.actions.setCurrentDashboardId(1));
      store.dispatch(dashboardSlice.actions.setCurrentDashboardTitle('Parent Dashboard'));

      store.dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: 2,
          title: 'Child Dashboard',
          templateId: 10,
          assetType: 5,
          assetId: 100,
        }),
      );

      const state = store.getState().dashboard;
      expect(state.dashboardCrumb).toHaveLength(2);
      expect(state.dashboardCrumb[0]).toEqual({
        dashboardId: 1,
        parentDashboardId: null,
        templateId: 10,
        dashboardTitle: 'Parent Dashboard',
        assetType: 5,
        assetId: 100,
      });
      expect(state.dashboardCrumb[1]).toEqual({
        dashboardId: 2,
        parentDashboardId: 1,
        templateId: 10,
        dashboardTitle: 'Child Dashboard',
        assetType: 5,
        assetId: 100,
      });
    });

    it('should remove dashboard crumb', () => {
      // Set up crumbs
      store.dispatch(dashboardSlice.actions.setCurrentDashboardId(1));
      store.dispatch(dashboardSlice.actions.setCurrentDashboardTitle('Dashboard 1'));
      store.dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: 2,
          title: 'Dashboard 2',
        }),
      );
      store.dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: 3,
          title: 'Dashboard 3',
        }),
      );

      // Remove crumb at index 1
      store.dispatch(dashboardSlice.actions.removeDashboardCrumb(1));

      const state = store.getState().dashboard;
      expect(state.dashboardCrumb).toHaveLength(2);
      expect(state.dashboardCrumb[1].dashboardId).toBe(2);
    });

    it('should reset dashboard crumb', () => {
      // Set up crumbs
      store.dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: 1,
          title: 'Dashboard 1',
        }),
      );

      store.dispatch(dashboardSlice.actions.resetDashboardCrumb());

      const state = store.getState().dashboard;
      expect(state.dashboardCrumb).toEqual([]);
    });
  });

  describe('Template Actions', () => {
    it('should set template', () => {
      const templateData = {
        assetTemplate: 1,
        assetType: 2,
        metrics: [10, 20, 30],
        idToName: { '10': 'Temperature', '20': 'Pressure' },
      };

      store.dispatch(dashboardSlice.actions.setTemplate(templateData));

      const state = store.getState().dashboard;
      expect(state.template.assetTemplate).toBe(1);
      expect(state.template.assetType).toBe(2);
      expect(state.template.metrics).toEqual([10, 20, 30]);
      expect(state.template.idToName).toEqual(templateData.idToName);
    });

    it('should set asset type', () => {
      store.dispatch(dashboardSlice.actions.setAssetType(5));

      const state = store.getState().dashboard;
      expect(state.template.assetType).toBe(5);
    });

    it('should set asset template', () => {
      store.dispatch(dashboardSlice.actions.setAssetTemplate(3));

      const state = store.getState().dashboard;
      expect(state.template.assetTemplate).toBe(3);
    });

    it('should set template name', () => {
      store.dispatch(dashboardSlice.actions.setTemplateName('My Template'));

      const state = store.getState().dashboard;
      expect(state.template.templateName).toBe('My Template');
    });

    it('should set template ID', () => {
      store.dispatch(dashboardSlice.actions.setTemplateId(42));

      const state = store.getState().dashboard;
      expect(state.template.templateId).toBe(42);
    });

    it('should set metrics', () => {
      const metrics = [1, 2, 3, 4, 5];
      store.dispatch(dashboardSlice.actions.setMetrics(metrics));

      const state = store.getState().dashboard;
      expect(state.template.metrics).toEqual(metrics);
    });

    it('should set metrics ID to name mapping', () => {
      store.dispatch(
        dashboardSlice.actions.setMetricsIdToName({
          metricId: '100',
          metricName: 'Temperature Sensor',
        }),
      );

      const state = store.getState().dashboard;
      expect(state.template.idToName['100']).toBe('Temperature Sensor');
    });

    it('should unset metrics ID to name mapping', () => {
      // First set a mapping
      store.dispatch(
        dashboardSlice.actions.setMetricsIdToName({
          metricId: '100',
          metricName: 'Temperature Sensor',
        }),
      );

      // Then unset it
      store.dispatch(
        dashboardSlice.actions.unsetMetricsIdToName({
          metricId: '100',
        }),
      );

      const state = store.getState().dashboard;
      expect(state.template.idToName['100']).toBeUndefined();
    });

    it('should create new dashboard template', () => {
      // Set some template data first
      store.dispatch(dashboardSlice.actions.setAssetType(5));
      store.dispatch(dashboardSlice.actions.setTemplateName('Old Template'));

      store.dispatch(dashboardSlice.actions.createNewDashboardTemplate());

      const state = store.getState().dashboard;
      expect(state.template.assetTemplate).toBe(0);
      expect(state.template.assetType).toBe(0);
      expect(state.template.metrics).toEqual([]);
      expect(state.template.idToName).toEqual({});
      expect(state.template.templateId).toBe(0);
      expect(state.template.templateName).toBe('');
    });

    it('should set template sample period', () => {
      store.dispatch(dashboardSlice.actions.setTemplateSamplePeriod(5));

      const state = store.getState().dashboard;
      expect(state.template.topPanel.samplePeriod).toBe(5);
    });

    it('should set template asset timezone', () => {
      store.dispatch(dashboardSlice.actions.setTemplateAssetTz(false));

      const state = store.getState().dashboard;
      expect(state.template.topPanel.assetTz).toBe(false);
    });

    it('should set template refresh interval', () => {
      store.dispatch(dashboardSlice.actions.setTemplateRefreshInterval(30));

      const state = store.getState().dashboard;
      expect(state.template.topPanel.refreshInterval).toBe(30);
    });

    it('should set template time range type', () => {
      store.dispatch(dashboardSlice.actions.setTemplateTimeRangeType(8));

      const state = store.getState().dashboard;
      expect(state.template.topPanel.timeRangeType).toBe(8);
    });

    it('should set template chart dates', () => {
      const startDate = new Date('2023-01-01').getTime();
      const endDate = new Date('2023-12-31').getTime();

      store.dispatch(dashboardSlice.actions.setTemplateChartStartDate(new Date(startDate)));
      store.dispatch(dashboardSlice.actions.setTemplateChartEndDate(new Date(endDate)));

      const state = store.getState().dashboard;
      expect(state.template.chart.startDate).toBe(startDate);
      expect(state.template.chart.endDate).toBe(endDate);
    });
  });

  describe('New Dashboard Action', () => {
    it('should reset to new dashboard state', () => {
      // Set up some state
      store.dispatch(dashboardSlice.actions.setCurrentDashboardId(123));
      store.dispatch(dashboardSlice.actions.setCurrentDashboardTitle('Old Dashboard'));
      store.dispatch(dashboardSlice.actions.setFullScreen(true));
      store.dispatch(dashboardSlice.actions.setRightSideBar(true));
      store.dispatch(dashboardSlice.actions.setIsLeftPanelOpen(false));
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));

      // Add some widgets
      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'stats',
          layout: [{ i: 'a', x: 0, y: 0, w: 2, h: 2 }],
          layoutItem: { i: 'a', x: 0, y: 0, w: 2, h: 2 },
        }),
      );

      // Reset to new dashboard
      store.dispatch(dashboardSlice.actions.setNewDashboard());

      const state = store.getState().dashboard;
      expect(state.currentDashboardId).toBe(0);
      expect(state.dashboardTitle).toBe('');
      expect(state.fullScreen).toBe(false);
      expect(state.rightSideBar).toBe(false);
      expect(state.isLeftPanelOpen).toBe(true);
      expect(state.kisok).toBe(false);
      expect(state.mainPanel).toBe('chart');
      expect(state.desktopMobile).toBe(0);
      expect(state.responsiveLayouts).toEqual({
        desktop: { widgetLayout: [], widgets: [] },
        mobile: { widgetLayout: [], widgets: [] },
      });
      expect(state.widget.widgets).toEqual([]);
      expect(state.widget.widgetLayout).toEqual([]);
      expect(state.widget.lastWidgetId).toBe(0);
    });
  });

  describe('Widget Title Management', () => {
    beforeEach(() => {
      // Add some widgets with titles
      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'table',
          layout: [{ i: 'a', x: 0, y: 0, w: 2, h: 2 }],
          layoutItem: { i: 'a', x: 0, y: 0, w: 2, h: 2 },
        }),
      );

      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'stats',
          layout: [{ i: 'b', x: 0, y: 2, w: 2, h: 2 }],
          layoutItem: { i: 'b', x: 0, y: 2, w: 2, h: 2 },
        }),
      );

      // Set some selected titles and measures
      const state = store.getState().dashboard;
      const tableWidget = state.widget.widgets.find((w) => w.type === 'table');
      const statsWidget = state.widget.widgets.find((w) => w.type === 'stats');

      if (tableWidget) {
        store.dispatch(
          dashboardSlice.actions.setCurrentWidgetSettings({
            ...tableWidget,
            settings: {
              ...tableWidget.settings,
              selectedTitles: ['title1', 'title2'],
            },
          }),
        );
      }

      if (statsWidget) {
        store.dispatch(
          dashboardSlice.actions.setCurrentWidgetSettings({
            ...statsWidget,
            settings: {
              ...statsWidget.settings,
              selectedDbMeasureId: 'measure1',
            },
          }),
        );
      }
    });

    it('should remove specific template title from widgets', () => {
      store.dispatch(dashboardSlice.actions.removeTemplateSpecificTitleFromWidget('title1'));

      const state = store.getState().dashboard;
      const tableWidget = state.widget.widgets.find((w) => w.type === 'table');
      expect(tableWidget?.settings.selectedTitles).toEqual(['title2']);
    });

    it('should remove widget titles', () => {
      store.dispatch(dashboardSlice.actions.removeWidgetTitles());

      const state = store.getState().dashboard;
      const tableWidget = state.widget.widgets.find((w) => w.type === 'table');
      const statsWidget = state.widget.widgets.find((w) => w.type === 'stats');

      expect(tableWidget?.settings.selectedTitles).toEqual([]);
      expect(statsWidget?.settings.selectedDbMeasureId).toBe('');
      expect(state.template.idToName).toEqual({});
      expect(state.template.metrics).toEqual([]);
    });
  });

  describe('Chart Date Actions', () => {
    it('should set chart start date', () => {
      const startDate = new Date('2023-06-01');
      store.dispatch(dashboardSlice.actions.setChartStartDate(startDate));

      const state = store.getState().dashboard;
      expect(state.chart.startDate).toBe(startDate.getTime());
    });

    it('should set chart end date', () => {
      const endDate = new Date('2023-06-30');
      store.dispatch(dashboardSlice.actions.setChartEndDate(endDate));

      const state = store.getState().dashboard;
      expect(state.chart.endDate).toBe(endDate.getTime());
    });
  });

  describe('Top Panel Actions', () => {
    it('should set sample period', () => {
      store.dispatch(dashboardSlice.actions.setSamplePeriod(3));

      const state = store.getState().dashboard;
      expect(state.topPanel.samplePeriod).toBe(3);
    });

    it('should set asset timezone', () => {
      store.dispatch(dashboardSlice.actions.setAssetTz(false));

      const state = store.getState().dashboard;
      expect(state.topPanel.assetTz).toBe(false);
    });

    it('should set refresh interval', () => {
      store.dispatch(dashboardSlice.actions.setRefreshTimeInterval(60));

      const state = store.getState().dashboard;
      expect(state.topPanel.refreshInterval).toBe(60);
    });

    it('should set time range type', () => {
      store.dispatch(dashboardSlice.actions.setTimeRangeType(10));

      const state = store.getState().dashboard;
      expect(state.topPanel.timeRangeType).toBe(10);
    });

    it('should set top panel visibility', () => {
      store.dispatch(dashboardSlice.actions.setTopPanelVisibility(false));

      const state = store.getState().dashboard;
      expect(state.topPanel.isVisible).toBe(false);
    });
  });

  describe('Widget Deletion Actions', () => {
    beforeEach(() => {
      // Add some widgets
      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'stats',
          layout: [{ i: 'a', x: 0, y: 0, w: 2, h: 2 }],
          layoutItem: { i: 'a', x: 0, y: 0, w: 2, h: 2 },
        }),
      );

      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'table',
          layout: [{ i: 'b', x: 0, y: 2, w: 2, h: 2 }],
          layoutItem: { i: 'b', x: 0, y: 2, w: 2, h: 2 },
        }),
      );
    });

    // it('should delete widget', () => {
    //   const initialState = store.getState().dashboard;
    //   expect(initialState.widget.widgets).toHaveLength(2);

    //   store.dispatch(dashboardSlice.actions.deleteSpecificWidget('6')); // First widget ID

    //   const state = store.getState().dashboard;
    //   expect(state.widget.widgets).toHaveLength(1);
    //   expect(state.widget.widgets[0].id).toBe('7'); // Second widget remains
    //   expect(state.widget.deleteWidgets).toContain('6');
    // });
    it('should delete widget', () => {
      const initialState = store.getState().dashboard;
      const widgetIds = initialState.widget.widgets.map((w) => w.id);
      expect(widgetIds.length).toBeGreaterThanOrEqual(2);

      const widgetIdToDelete = widgetIds[0];
      const widgetIdToRemain = widgetIds[1];

      store.dispatch(dashboardSlice.actions.deleteSpecificWidget(widgetIdToDelete));

      const state = store.getState().dashboard;
      const remainingIds = state.widget.widgets.map((w) => w.id);

      expect(remainingIds).toHaveLength(1);
      expect(remainingIds[0]).toBe(widgetIdToRemain);
      expect(state.widget.deleteWidgets).toContain(widgetIdToDelete);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle cloning non-existent widget gracefully', () => {
      const initialState = store.getState().dashboard;
      const currentMode = store.getState().dashboard.desktopMobile === 0 ? 'desktop' : 'mobile';
      store.dispatch(dashboardSlice.actions.cloneWidget('non-existent-id'));

      const state = store.getState().dashboard;
      expect(state.widget.widgets).toEqual(initialState.widget.widgets);
      expect(state.widget.widgetLayout).toEqual(initialState.widget.widgetLayout);
      expect(state.responsiveLayouts?.[currentMode].widgets).toEqual(
        initialState.responsiveLayouts?.[currentMode].widgets,
      );
      expect(state.responsiveLayouts?.[currentMode].widgetLayout).toEqual(
        initialState.responsiveLayouts?.[currentMode].widgetLayout,
      );
    });

    it('should handle setting widget settings for non-existent widget', () => {
      const mockWidget: Widget = {
        id: 'non-existent',
        type: 'stats',
        settings: {
          title: { value: 'Test', isVisible: true, color: '#000000' },
          isValid: true,
          isDirty: false,
          mode: 'dashboard',
          isChildWidget: false,
          dashboardOrTemplate: 'dashboard',
          assetOrAssetType: null,
          dashboard: null,
          openDashboardInNewTab: false,
          isRealTime: false,
          retainPeriod: 60,
          refreshInterval: -1,
          dbMeasureIdToName: {},
          fontSize: 12,
          fontWeight: 'bolder',
          selectedAssetId: '',
          assetMeasure: { assetId: '', measureId: [] },
          selectedDbMeasureId: '',
          showMin: true,
          showMinLable: true,
          minColor: '#000000',
          minBorder: true,
          min: { label: 'Min', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showMax: true,
          showMaxLabel: true,
          maxColor: '#000000',
          maxBorder: true,
          max: { label: 'Max', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showAvg: true,
          showAvgLabel: true,
          avgColor: '#000000',
          avgBorder: true,
          avg: { label: 'Avg', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showDelta: true,
          showDeltaLabel: true,
          deltaColor: '#000000',
          deltaBorder: true,
          delta: { label: 'Delta', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showSum: true,
          showSumLabel: true,
          sumColor: '#000000',
          sumBorder: true,
          sum: { label: 'Sum', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showTotal: true,
          showTotalLabel: true,
          totalColor: '#000000',
          totalBorder: true,
          total: { label: 'Total', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          showCurrent: true,
          showCurrentLabel: true,
          currentColor: '#000000',
          showCurrentBorder: true,
          current: { label: 'Last Value', fontSize: 20, fontWeight: 'bolder', color: '#000000' },
          aggBy: 1,
          samplePeriod: 2,
          globalSamplePeriod: false,
          overrideAssetTz: false,
          overrideAssetTzValue: false,
          overrideGlobalSettings: false,
          isRelativeToGlboalEndTime: false,
          timeRange: 6,
          startDate: Date.now(),
          endDate: Date.now(),
        },
      };
      const currentMode = store.getState().dashboard.desktopMobile === 0 ? 'desktop' : 'mobile';
      const initialState = store.getState().dashboard;

      store.dispatch(dashboardSlice.actions.setCurrentWidgetSettings(mockWidget));

      const state = store.getState().dashboard;
      expect(state.widget.widgets).toEqual(initialState.widget.widgets);
      expect(state.widget.widgets).toEqual(initialState.widget.widgets);
      expect(state.responsiveLayouts?.[currentMode].widgets).toEqual(
        initialState.responsiveLayouts?.[currentMode].widgets,
      );
    });

    it('should handle removing node ID that does not exist', () => {
      store.dispatch(dashboardSlice.actions.setSelectedNodeIds(['node-1', 'node-2']));
      store.dispatch(dashboardSlice.actions.removeSelectedNodeId('non-existent-node'));

      const state = store.getState().dashboard;
      expect(state.tree.selectedNodeIds).toEqual(['node-1', 'node-2']);
    });

    it('should handle unselectCheckbox for non-existent metric', () => {
      store.dispatch(
        dashboardSlice.actions.selectCheckbox({
          assetId: 'asset-1',
          metricId: 'metric-1',
          metricName: 'Temperature',
        }),
      );

      store.dispatch(
        dashboardSlice.actions.unselectCheckbox({
          assetId: 'asset-1',
          metricId: 'non-existent-metric',
        }),
      );

      const state = store.getState().dashboard;
      expect(state.tree.dbMeasureIdToName['metric-1']).toBe('Temperature');
      expect(state.isDirty).toBe(true);
    });

    it('should handle responsive layout actions when responsiveLayouts is undefined', () => {
      // Manually set responsiveLayouts to undefined to test edge case
      const stateWithoutResponsive = {
        ...store.getState().dashboard,
        responsiveLayouts: undefined,
      };

      // Create new store with this state
      const testStore = configureStore({
        reducer: { dashboard: dashboardSlice.reducer },
        preloadedState: { dashboard: stateWithoutResponsive },
      });

      // Should not crash when updating layout
      testStore.dispatch(dashboardSlice.actions.updateLayout([{ i: '1', x: 0, y: 0, w: 6, h: 4 }]));

      const state = testStore.getState().dashboard;
      expect(state.widget.widgetLayout).toEqual([{ i: '1', x: 0, y: 0, w: 6, h: 4 }]);
    });
  });

  describe('State Immutability', () => {
    it('should not mutate original state when updating', () => {
      const originalState = store.getState().dashboard;
      const originalWidgets = originalState.widget.widgets;
      const originalLayout = originalState.widget.widgetLayout;

      store.dispatch(
        dashboardSlice.actions.addWidget({
          widgetMode: 'dashboard',
          type: 'stats',
          layout: [{ i: 'a', x: 0, y: 0, w: 2, h: 2 }],
          layoutItem: { i: 'a', x: 0, y: 0, w: 2, h: 2 },
        }),
      );

      // Original state should remain unchanged
      expect(originalWidgets).toEqual([]);
      expect(originalLayout).toEqual([]);

      // New state should have the widget
      const newState = store.getState().dashboard;
      expect(newState.widget.widgets).toHaveLength(1);
    });

    it('should create new arrays when updating layouts', () => {
      const layout1 = [{ i: '1', x: 0, y: 0, w: 6, h: 4 }];
      const layout2 = [{ i: '2', x: 6, y: 0, w: 6, h: 4 }];

      store.dispatch(dashboardSlice.actions.setWidgetsLayout(layout1));
      const state1 = store.getState().dashboard;

      store.dispatch(dashboardSlice.actions.setWidgetsLayout(layout2));
      const state2 = store.getState().dashboard;

      // States should have different layout arrays
      expect(state1.widget.widgetLayout).not.toBe(state2.widget.widgetLayout);
      expect(state1.widget.widgetLayout).toEqual(layout1);
      expect(state2.widget.widgetLayout).toEqual(layout2);
    });
  });
});

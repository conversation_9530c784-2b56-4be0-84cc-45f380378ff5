import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Dash, Data, Layout } from 'plotly.js';
import { MutableRefObject, useMemo, useRef } from 'react';
import { Datasource, UnitOfMeasure } from '~/measurements/domain/types';
import { Asset } from '~/types/asset';
import { TimeRangeOptions } from '~/types/dashboard';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import {
  KPIBarChart,
  KPIColorBox,
  KpiCurrentWidget,
  KPIPercentage,
  KPISparkline,
  KPIValueIndicator,
  StatsWidget,
  Widget
} from '~/types/widgets';
dayjs.extend(utc);

export function capitalizeFirstLetter(input: string): string {
  return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
}

export function formatMetricLabel(input: string): string {
  if (!input) return '';
  return input
    .split('\\') // Split the string by backslash
    .pop()! // Take the last segment
    .replace(/_/g, ' ') // Replace underscores with spaces
    .split(' ') // Split the string by spaces
    .map(capitalizeFirstLetter) // Capitalize the first letter of each word
    .join(' '); // Join the words with spaces
}

export function formatMetricLabelForTree(input: string): string {
  if (!input) return '';
  return input
    .split('\\') // Split the string by backslash
    .pop()! // Take the last segment
    .replace(/_/g, ' ') // Replace underscores with spaces
    .split(' ') // Split the string by spaces
    .join(' '); // Join the words with spaces
}
export function convertMeasureBackslashes(str: string) {
  return str.replace(/\\/g, '>');
}
export function formatMetricTag(input: string): string {
  return input
    .split('\\') // Split the string by backslash
    .map(
      (str) =>
        str
          .replace(/_/g, ' ') // Replace underscores with spaces
          .split(' ') // Split the string by spaces
          .map(capitalizeFirstLetter) // Capitalize the first letter of each word
          .join(' '), // Join the words with spaces
    )
    .join('/'); // Join the words with /
}

export function getPreviousDate(minutes: number) {
  if (minutes === -1) {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
    const startOfWeek = new Date(now.setDate(diff));
    startOfWeek.setHours(0, 0, 0, 0); // Set the time to 12 AM
    return startOfWeek.getTime();
  } else if (minutes === -2) {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1).getTime();
  } else if (minutes === -3) {
    const now = new Date();
    return new Date(now.getFullYear(), 0, 1).getTime();
  } else if (minutes === -4) {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
  } else {
    return new Date(new Date().getTime() - minutes * 60000).getTime();
  }
}

export function getPreviousDateRelativeToEndTime(minutes: number, referenceTime?: number) {
  const baseTime = referenceTime ? new Date(referenceTime) : new Date();
  if (minutes === -1) {
    const dayOfWeek = baseTime.getDay();
    const diff = baseTime.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
    const startOfWeek = new Date(baseTime.setDate(diff));
    startOfWeek.setHours(0, 0, 0, 0); // Set the time to 12 AM
    return startOfWeek.getTime();
  } else if (minutes === -2) {
    return new Date(baseTime.getFullYear(), baseTime.getMonth(), 1).getTime();
  } else if (minutes === -3) {
    return new Date(baseTime.getFullYear(), 0, 1).getTime();
  } else if (minutes === -4) {
    return new Date(baseTime.getFullYear(), baseTime.getMonth(), baseTime.getDate()).getTime();
  } else {
    return new Date(baseTime.getTime() - minutes * 60000).getTime();
  }
}

export function getDashboardId(customerId: number) {
  if (customerId === 9) {
    return 2;
  } else if (customerId === 84) {
    return 12;
  }
  return 0;
}

export function formatDate(date: Date, dateFormat: string): string {
  return dayjs(date).format(dateFormat);
}

export function fortmatUTCDate(date: Date, dateFormat: string): string {
  return dayjs(date).utc().format(dateFormat);
}

export function formatChartDate(date: Date): string {
  // return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
  return dayjs(date).utc().format('YYYY-MM-DD HH:mm:ss');
}

export function formatChartDateToAssetTz(date: Date): string {
  return dayjs(date).utc().format('YYYY-MM-DD HH:mm:ss');
}
export function roundNumberFormatter(num: number): number {
  if (!isFinite(num)) return 0;
  if (num === 0) return 0;

  const absNum = Math.abs(num);
  const decimalPlaces = absNum < 1 ? 4 : absNum < 10 ? 3 : absNum < 100 ? 2 : absNum < 1000 ? 1 : 0;

  return absNum < 1000 ? parseFloat(num.toFixed(decimalPlaces)) : Math.round(num);
  //   0.000123	'0.0001'	< 1, so 4 decimal places
  // 1.2345	'1.235'	< 10, so 3 decimal places
  // 23.456	'23.46'	< 100, so 2 decimal places
  // 456.78	'456.8'	< 1000, so 1 decimal place
  // 1234.56	'1235'	>= 1000, round to integer
  // NaN	'0'	Not a finite number
  // Infinity	'0'	Not a finite number
  // 0	'0'	Zero case
}

export const formatNumber = (
  value: number | string,
  options?: {
    decimals?: number;
    compact?: boolean;
    prefix?: string;
    suffix?: string;
    locale?: string;
  },
): string => {
  //   Input	Options	Output	Explanation
  // formatNumber(0.00012345)	(no options)	"0.0001"	Adaptive: <1 → 4 decimals
  // formatNumber(1.23456)	(no options)	"1.235"	Adaptive: <10 → 3 decimals
  // formatNumber(12.3456)	(no options)	"12.35"	Adaptive: <100 → 2 decimals
  // formatNumber(123.456)	(no options)	"123.5"	Adaptive: <1000 → 1 decimal
  // formatNumber(1234.56)	(no options)	"1,235"	Adaptive: ≥1000 → 0 decimals
  const { decimals, compact = false, prefix = '', suffix = '', locale = 'en-US' } = options || {};

  const rawNum = Number(value);
  if (!isFinite(rawNum)) return '-';

  const num = typeof decimals === 'number' ? rawNum : roundNumberFormatter(rawNum); // Use adaptive rounding if no decimals specified

  const effectiveDecimals =
    typeof decimals === 'number'
      ? decimals
      : (() => {
          const abs = Math.abs(rawNum);
          if (abs < 1) return 4;
          if (abs < 10) return 3;
          if (abs < 100) return 2;
          if (abs < 1000) return 1;
          return 0;
        })();

  return (
    prefix +
    num.toLocaleString(locale, {
      minimumFractionDigits: effectiveDecimals,
      maximumFractionDigits: effectiveDecimals,
      notation: compact ? 'compact' : 'standard',
      compactDisplay: 'short',
    }) +
    suffix
  );
};

export function roundNumber(num: number): string {
  if (!isFinite(num)) return '-';
  if (num === 0) return '0';

  const absNum = Math.abs(num);
  const decimalPlaces = absNum < 1 ? 4 : absNum < 10 ? 3 : absNum < 100 ? 2 : absNum < 1000 ? 1 : 0;

  return absNum < 1000 ? num?.toFixed(decimalPlaces) : Math.round(num).toString();
}
export const fontWeights = [
  '100',
  '200',
  '300',
  '400',
  '500',
  '600',
  '700',
  '800',
  '900',
  'bold',
  'bolder',
  'lighter',
];

export const defaultOverrideDateSettings = (): {
  overrideGlobalSettings: boolean;
  isRelativeToGlboalEndTime: boolean;
  startDate: number;
  endDate: number;
  timeRange: number;
} => {
  const timeRange = 8;
  return {
    overrideGlobalSettings: false,
    startDate: getPreviousDate(TimeRangeOptions[timeRange].serverValue),
    endDate: new Date().getTime(),
    timeRange: timeRange,
    isRelativeToGlboalEndTime: false,
  };
};

export const areKeysSame = <K extends string | number, V>(
  prevKeysRef: MutableRefObject<K[]>,
  newData: Record<K, V>,
) => {
  const newKeys = Object.keys(newData) as K[];
  const prevKeys = prevKeysRef.current;

  if (newKeys.length !== prevKeys.length) {
    return false;
  }

  for (const key of newKeys) {
    if (!prevKeys.includes(key)) {
      return false;
    }
  }

  return true;
};

export const useGetKeys = <K extends string, V>(keyToValue: Record<K, V>) => {
  const prevKeysRef = useRef<K[]>([]);
  return useMemo(() => {
    if (areKeysSame(prevKeysRef, keyToValue)) {
      return prevKeysRef.current;
    } else {
      const newKeys = Object.keys(keyToValue);
      prevKeysRef.current = newKeys as K[];
      return newKeys;
    }
  }, [keyToValue]);
};

export const getMeasurementsFromTree = (selectedNodeIds: string[]): string[] => {
  return selectedNodeIds.map((currentSelectedNodeId) => {
    if (currentSelectedNodeId.startsWith('m:')) {
      return currentSelectedNodeId.split(':')[2];
    }
    return currentSelectedNodeId;
  });
};

export const findDeleteMeasures = (widgets: Widget[], selectedNodesList: string[]) => {
  const deleteList = widgets
    .map((currentWidget) => {
      if (
        currentWidget.type === 'chart' &&
        (currentWidget.settings.chartType === 'scatter' ||
          currentWidget.settings.chartType === 'bar')
      ) {
        const list = currentWidget.settings.settings.selectedTitles.filter(
          (title) => !selectedNodesList.includes(title),
        );
        return list;
      }
      if (
        currentWidget.type === 'chart' &&
        (currentWidget.settings.chartType === 'heatmap' ||
          currentWidget.settings.chartType === 'indicator' ||
          currentWidget.settings.chartType === 'bullet')
      ) {
        const list = selectedNodesList.includes(
          currentWidget.settings.settings.selectedDbMeasureId,
        );
        return !list ? [currentWidget.settings.settings.selectedDbMeasureId] : [];
      }
      if (
        currentWidget.type === 'table' ||
        currentWidget.type === 'image' ||
        currentWidget.type === 'solar_panel' ||
        currentWidget.type === 'vertical' ||
        currentWidget.type === 'voltage'
      ) {
        const list = currentWidget.settings.selectedTitles.filter(
          (title) => !selectedNodesList.includes(title),
        );
        return list;
      }
      if (currentWidget.type === 'stats') {
        const list = selectedNodesList.includes(currentWidget.settings.selectedDbMeasureId);
        return !list ? [currentWidget.settings.selectedDbMeasureId] : [];
      }
    })
    .filter((measures) => measures && measures?.length > 0)
    .flat();
  return Array.from(new Set(deleteList));
};

export function showMeanValue(
  layout: { layout: Partial<Layout> },
  traces: Data[],
  meanSetting: { meanColor: string; meanStyle: 'solid' | 'dash' | 'dot'; meanName: string },
) {
  if (!layout.layout.annotations) {
    layout.layout.annotations = [];
  }
  const firstTrace: Data = traces[0];
  const lastTrace: Data = traces[traces.length - 1];
  if (firstTrace && lastTrace) {
    //eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    const firstYValue = firstTrace.y[0];
    //eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    const lastYValue = lastTrace.y[lastTrace.y.length - 1];
    const mean = (firstYValue + lastYValue) / 2;
    const thresholdValue = mean;
    const threshold = showThresholdValue(layout, {
      thresholdColor: meanSetting.meanColor ?? '#000000',
      thresholdName: meanSetting.meanName ?? 'Mean',
      thresholdStyle: meanSetting.meanStyle,
      thresholdValue,
    });
    layout.layout = threshold.layout;
  }
  return { ...layout };
}
export function showThresholdValue(
  layout: { layout: Partial<Layout> },
  treshdold: {
    thresholdName: string;
    thresholdValue: number;
    thresholdColor: string;
    thresholdStyle: string;
  },
) {
  if (!layout.layout.annotations) {
    layout.layout.annotations = [];
  }

  layout.layout.annotations.push({
    xref: 'paper',
    yref: 'y',
    x: 1,
    xanchor: 'left',
    bgcolor: 'rgba(255, 255, 255)',
    y: treshdold?.thresholdValue,
    text: treshdold?.thresholdName
      ? `${treshdold?.thresholdName} : ${treshdold?.thresholdValue}`
      : `${treshdold?.thresholdValue}`,
    showarrow: false,
  });
  if (!layout.layout.shapes) {
    layout.layout.shapes = [];
  }
  layout.layout.shapes.push({
    type: 'line',
    x0: 0,
    x1: 1,
    xref: 'paper',
    y0: treshdold?.thresholdValue,
    y1: treshdold?.thresholdValue,
    yref: 'y',
    line: {
      color: treshdold?.thresholdColor ?? 'yellow',
      width: 2,
      dash: (treshdold?.thresholdStyle as Dash) ?? 'solid',
    },
  });

  return { ...layout };
}

export const mapListToOptions = (
  typeList: { id: number; name: string }[],
): { id: string; label: string }[] => {
  return typeList.map((type) => ({ id: type.id.toString(), label: type.name }));
};

export const showMinMaxAvg = (
  layout: Partial<Layout>,
  minMaxAvg: {
    min: {
      show: boolean;
    };
    max: {
      show: boolean;
    };
    avg: {
      show: boolean;
    };
  },
  min: string,
  max: string,
  avg: string,
) => {
  layout.shapes = [];
  layout.annotations = [];
  const { min: minShow, avg: avgShow, max: maxShow } = minMaxAvg;
  if (minShow?.show) {
    layout.shapes.push({
      type: 'line',
      x0: 0,
      x1: 1,
      xref: 'paper',
      y0: min,
      y1: min,
      yref: 'y',
      line: {
        color: '#d3d3d3',
        width: 2,
        dash: 'dashdot',
      },
      label: {
        xanchor: 'center',
        text: `Min: ${min}`,
      },
    });
    layout.annotations.push({
      xref: 'paper',
      yref: 'y',
      x: 1,
      xanchor: 'left',
      y: min,
      text: `Min: ${min}`,
      showarrow: false,
    });
  }
  if (maxShow?.show) {
    layout.shapes.push({
      type: 'line',
      x0: 0,
      x1: 1,
      xref: 'paper',
      y0: max,
      y1: max,
      yref: 'y',
      line: {
        color: '#d3d3d3',
        width: 2,
        dash: 'dashdot',
      },
      label: {
        yanchor: 'top',
        text: `Max: ${max}`,
        textposition: 'top center',
        xanchor: 'center',
      },
    });
    layout.annotations.push({
      xref: 'paper',
      yref: 'y',
      x: 1,
      xanchor: 'left',
      y: max,
      text: `Max: ${max}`,
      showarrow: false,
    });
  }
  if (avgShow?.show) {
    layout.shapes.push({
      type: 'line',
      x0: 0,
      x1: 1,
      xref: 'paper',
      y0: avg,
      y1: avg,
      yref: 'y',
      line: {
        color: '#d3d3d3',
        width: 2,
        dash: 'dashdot',
      },
      label: {
        xanchor: 'center',
        text: `Avg: ${avg}`,
      },
    });
    layout.annotations.push({
      xref: 'paper',
      yref: 'y',
      x: 1,
      xanchor: 'left',
      y: avg,
      text: `Avg: ${avg}`,
      showarrow: false,
    });
  }
  return { ...layout };
};
export function getBrowserTimezoneName() {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

export function getBrowserTimezoneUTC() {
  const offset = new Date().getTimezoneOffset(); // Get offset in minutes
  const sign = offset > 0 ? '-' : '+'; // Determine sign based on offset
  const hours = String(Math.abs(offset) / 60).padStart(2, '0');
  const minutes = String(Math.abs(offset) % 60).padStart(2, '0');
  return `UTC${sign}${hours}:${minutes}`;
}

export function checkTrendUp(data: [number, number][]) {
  if (data.length < 2) {
    return 'same'; // Not enough data points to determine the trend
  }
  const firstValue = data[0][1]; // Get the value of the first data point
  const lastValue = data[data.length - 1][1]; // Get the value of the last data point
  if (lastValue === firstValue) {
    return 'same';
  }
  return lastValue > firstValue ? 'up' : 'down'; // Return true if the trend is up, false if the trend is down
}

export function hexToRgbA(hex: string) {
  let c: any;
  if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
    c = hex.substring(1).split('');
    if (c.length == 3) {
      c = [c[0], c[0], c[1], c[1], c[2], c[2]];
    }
    c = '0x' + c.join('');
    return [(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',');
  }
  throw new Error('Bad Hex');
}

export function calulationXaxisValue(seriesData: SingleScatterTimeSeriesData, useAssetTz: boolean) {
  return seriesData?.['ts,val']?.map(([timestamp]) =>
    useAssetTz
      ? formatChartDateToAssetTz(new Date(timestamp))
      : formatChartDate(new Date(timestamp)),
  );
}

export function calulationYaxisValue(seriesData: SingleScatterTimeSeriesData) {
  return seriesData?.['ts,val']?.map(([, value]) => value);
}

export function calculateSumAndDelta(
  unitsOfMeasure: UnitOfMeasure,
  {
    showDelta,
    deltaLabel,
    showSum,
    sumLabel,
  }: {
    showSum: boolean;
    sumLabel: string;
    showDelta: boolean;
    deltaLabel: string;
  },
  traces: Data[],
  layout: Partial<Layout>,
  yData: number[],
) {
  if (showSum) {
    const sum = roundNumber(
      traces.reduce((total, trace) => {
        //eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        const yValues = yData as number[];
        return total + yValues.reduce((sum, value) => sum + value, 0);
      }, 0),
    );
    layout.title += `<br><br><sup>${sumLabel}:${sum} ${unitsOfMeasure.name}</sup>`;
  }
  if (showDelta) {
    //eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    const firstTraceValues = traces[0]?.y as number[];
    //eslint-disable-next-line
    //@ts-ignore
    const lastTraceValues = traces[traces.length - 1]?.y as number[];

    const firstValue = firstTraceValues ? firstTraceValues[0] : undefined;
    const lastValue = lastTraceValues ? lastTraceValues[lastTraceValues.length - 1] : undefined;
    const delta = roundNumber((lastValue ?? 0) - (firstValue ?? 0));
    layout.title += `  <sup>${deltaLabel}:${delta} ${unitsOfMeasure.name}</sup>`;
  }
  return layout;
}

const buildPath = (assetTypesMap: Map<number, Asset>, asset: Asset): string => {
  if (asset.parentIds === null || asset.parentIds.length === 0) {
    return asset.tag;
  } else {
    // Assuming the first parent_id is the immediate parent (if there are multiple parents, this may need adjusting)
    const parentAssetId = asset.parentIds[0];
    const parentAsset = assetTypesMap.get(parentAssetId ?? 0);
    if (!parentAsset) {
      return '';
    }
    return `${buildPath(assetTypesMap, parentAsset)} > ${asset.tag}`;
  }
};

export const assetsPathMapper = (assets: Asset[]) => {
  const assetsMap = new Map<number, Asset>();
  assets.forEach((asset) => assetsMap.set(asset.id, asset));
  const mappedAssets = assets
    .filter(
      (asset) =>
        (asset.parentIds.length === 1 || asset.parentIds.length === 0) &&
        !asset.parentIds.includes(asset.id),
    )
    .map((asset) => ({
      value: asset.id,
      id: asset.id,
      label: buildPath(assetsMap, asset),
    }));

  return mappedAssets.filter((asset) => asset.label !== '');
};

export const aggGenerator = (
  settings: KPIBarChart,
  measureData: AssetMeasurementDetails,
  aggBy: number,
  assetTzOverride: boolean,
  assetTzOverrideValue: boolean,
) => {
  const aggs = [];
  switch (settings.selectedSamplePeriod) {
    case 'D': {
      const now = new Date();
      const lastDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).getTime();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
      aggs.push(
        ...[
          {
            meas_id: measureData.measurementId,
            start: today,
            end: new Date(Date.now()).getTime(),
            agg: 'twa',
            agg_period: '1hr',
            use_asset_tz: assetTzOverride,
            browser_tz: assetTzOverrideValue,
          },
          ...(settings.showPrevious
            ? [
                {
                  meas_id: measureData.measurementId,
                  start: lastDay,
                  end: today,
                  agg: 'twa',
                  agg_period: '1hr',
                  use_asset_tz: assetTzOverride,
                  browser_tz: assetTzOverrideValue,
                },
              ]
            : []),
        ],
      );
      break;
    }
    case 'W': {
      const now = new Date();
      const dayOfWeek = now.getDay();
      const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
      const startOfWeek = new Date(now.setDate(diff));
      startOfWeek.setHours(0, 0, 0, 0); // Set the time to 12 AM
      const thisWeek = startOfWeek.getTime();
      const lastWeek = new Date(thisWeek - 7 * 24 * 60 * 60 * 1000).getTime();
      aggs.push(
        ...[
          {
            meas_id: measureData.measurementId,
            start: thisWeek,
            end: new Date(Date.now()).getTime(),
            agg: 'twa',
            agg_period: 'DAILY',
            use_asset_tz: assetTzOverride,
            browser_tz: assetTzOverrideValue,
          },
          ...(settings.showPrevious
            ? [
                {
                  meas_id: measureData.measurementId,
                  start: lastWeek,
                  end: thisWeek,
                  agg: 'twa',
                  agg_period: 'DAILY',
                  use_asset_tz: assetTzOverride,
                  browser_tz: assetTzOverrideValue,
                },
              ]
            : []),
        ],
      );
      break;
    }
    case 'M': {
      const now = new Date();
      const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1).getTime();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).getTime();
      aggs.push(
        ...[
          {
            meas_id: measureData.measurementId,
            start: currentMonth,
            end: new Date(Date.now()).getTime(),
            agg: 'twa',
            agg_period: 'DAILY',
            use_asset_tz: assetTzOverride,
            browser_tz: assetTzOverrideValue,
          },
          ...(settings.showPrevious
            ? [
                {
                  meas_id: measureData.measurementId,
                  start: lastMonth,
                  end: currentMonth,
                  agg: 'twa',
                  agg_period: 'DAILY',
                  use_asset_tz: assetTzOverride,
                  browser_tz: assetTzOverrideValue,
                },
              ]
            : []),
        ],
      );
      break;
    }
    case '6M':
      aggs.push(
        ...[
          ...[
            {
              meas_id: measureData.measurementId,
              start: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).getTime(),
              end: new Date(Date.now()).getTime(),
              agg: 'twa',
              agg_period: 'MONTHLY',
              use_asset_tz: assetTzOverride,
              browser_tz: assetTzOverrideValue,
            },
            ...(settings.showPrevious
              ? [
                  {
                    meas_id: measureData.measurementId,
                    start: new Date(Date.now() - 12 * 30 * 24 * 60 * 60 * 1000).getTime(),
                    end: new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).getTime(),
                    agg: 'twa',
                    agg_period: 'MONTHLY',
                    use_asset_tz: assetTzOverride,
                    browser_tz: assetTzOverrideValue,
                  },
                ]
              : []),
          ],
        ],
      );
      break;
    case 'Y': {
      const now = new Date();
      const currentYear = new Date(now.getFullYear(), 0, 1).getTime();
      const lastYear = new Date(now.getFullYear() - 1, 0, 1).getTime();
      aggs.push(
        ...[
          {
            meas_id: measureData.measurementId,
            start: currentYear,
            end: new Date(Date.now()).getTime(),
            agg: 'twa',
            agg_period: 'MONTHLY',
            use_asset_tz: assetTzOverride,
            browser_tz: assetTzOverrideValue,
          },
          ...(settings.showPrevious
            ? [
                {
                  meas_id: measureData.measurementId,
                  start: lastYear,
                  end: currentYear,
                  agg: 'twa',
                  agg_period: 'MONTHLY',
                  use_asset_tz: assetTzOverride,
                  browser_tz: assetTzOverrideValue,
                },
              ]
            : []),
        ],
      );
      break;
    }
  }
  return aggs;
};

export const getWeekdays = (weekday: number) => {
  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  return days[weekday >= 0 ? weekday : 7];
};
export const getIsMeasureTimeVaringFactor = (
  dataSource: Datasource[],
  measureData: AssetMeasurementDetails,
) => {
  const dataSourceName = dataSource.find((source) => source.id === measureData.datasourceId)?.name;
  return dataSourceName === 'TimeVaryingFactor';
};

export const hasNoMeasureSelected = (
  settings:
    | KPIBarChart
    | StatsWidget
    | KPIValueIndicator
    | KPIColorBox
    | KPISparkline
    | KPIPercentage
    | KpiCurrentWidget,
) => {
  if (settings.mode === 'template' && settings.selectedDbMeasureId === '') {
    return true;
  }
  return (
    settings.mode === 'dashboard' &&
    (settings.assetMeasure?.measureId?.length === 0 ||
      settings.assetMeasure?.measureId?.some((measure) => measure === ''))
  );
};

export const formatDiagramElementName = (text: string) => {
  return text.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, (str) => str.toUpperCase());
};

import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://dev.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.getByRole('button', { name: 'Alerts' }).click();
  await page.waitForTimeout(2000)
  await page.getByRole('menuitem', { name: 'Excursions' }).click();
  await page.waitForTimeout(10000)
  await page.close();
});

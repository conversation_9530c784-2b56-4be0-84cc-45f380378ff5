// npm test -- --testPathPattern=MeasurementDashboard.test.tsx --verbose --watchAll=false
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import React from 'react';
import { Provider } from 'react-redux';

import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { ImageWidgetSettings } from '~/types/widgets';
import MeasurementDashboard from '../MeasurementDashboard';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock API hooks
jest.mock('~/redux/api/assetsApi', () => ({
  useGetAllAssetQuery: jest.fn(() => ({
    data: [
      { id: 1, tag: 'Asset 1', assetTypeId: 1 },
      { id: 2, tag: 'Asset 2', assetTypeId: 2 },
    ],
    isFetching: false,
  })),
  useGetAllBackOfficeAssetTypesQuery: jest.fn(() => ({
    data: [
      { id: 1, name: 'Type 1' },
      { id: 2, name: 'Type 2' },
    ],
    isLoading: false,
    isSuccess: true,
  })),
}));

jest.mock('~/redux/api/dashboardApi', () => ({
  useGetDashboardByCustomerIdQuery: jest.fn(() => ({
    data: {
      items: [
        { id: 1, title: 'Dashboard 1' },
        { id: 2, title: 'Dashboard 2' },
      ],
    },
    isLoading: false,
  })),
}));

jest.mock('~/redux/api/dashboardTemplate', () => ({
  dashboardTemplateApi: {
    endpoints: {
      getDashboardTemplateDetails: {
        initiate: jest.fn(() => ({
          isError: false,
          error: null,
          data: {
            id: 1,
            data: JSON.stringify({
              widget: { widgets: [] },
              topPanel: { timeRangeType: 6, refreshInterval: -1, samplePeriod: 2, assetTz: true },
              chart: { startDate: new Date().getTime(), endDate: new Date().getTime() },
              desktopMobile: 0,
              responsiveLayouts: {
                desktop: { widgetLayout: [], widgets: [] },
                mobile: { widgetLayout: [], widgets: [] },
              },
            }),
            asset_template: {
              assetType: { id: 1, name: 'Test Type' },
            },
          },
        })),
      },
    },
  },
  useGetDashboardTemplatesQuery: jest.fn(() => ({
    data: {
      items: [
        { id: 1, title: 'Template 1' },
        { id: 2, title: 'Template 2' },
      ],
    },
    isLoading: false,
  })),
}));

jest.mock('~/redux/api/measuresApi', () => ({
  useGetAllMeasurementsQuery: jest.fn(() => ({
    data: [
      { id: 1, tag: 'Measurement 1', metric_id: 1 },
      { id: 2, tag: 'Measurement 2', metric_id: 2 },
    ],
  })),
}));

// Mock utils
jest.mock('~/utils/mappers/asset-type-mapper', () => ({
  assetTypePathMapperFilterTemplates: jest.fn((data) =>
    data.map((item: any) => ({ value: item.id, label: item.name })),
  ),
}));

jest.mock('~/utils/utils', () => ({
  assetsPathMapper: jest.fn((data) =>
    data.map((item: any) => ({ id: item.id, label: item.tag, value: item.id })),
  ),
  getPreviousDate: jest.fn(() => new Date().getTime()),
}));

// Mock router object
const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: {},
  asPath: '',
  route: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
  basePath: '',
};

// Helper function to create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        currentDashboardId: 1,
        customer: {
          id: 1,
          name: 'Test Customer',
          nameId: 'test',
          address: 'Test Address',
          logo: '',
          enabled: true,
        },
        ...initialState,
      },
    },
  });
};

const mockTheme = createTheme();

// Helper function to render component with providers
const renderWithProviders = (
  component: React.ReactElement,
  { store = createTestStore(), ...renderOptions } = {},
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>{children}</ThemeProvider>
    </Provider>
  );

  return {
    store,
    ...render(component, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Default props for the component
const defaultLabelAndUnits = {
  label: 'Test Label',
  unit: 'Test Unit',
  dashboard: { id: 1, title: 'Test Dashboard' },
  dashboardOrTemplate: 'template' as const,
  assetOrAssetType: 1,
  openDashboardInNewTab: false,
};

const defaultSettings: ImageWidgetSettings = {
  id: '1',
  type: 'image',
  mode: 'dashboard',
  isDirty: false,
  measureIdToImageTextDetails: {
    '1': {
      label: 'Test Label',
      unit: 'Test Unit',
      id: '1',
      positionX: 100,
      positionY: 100,
      value: '',
      dashboard: { id: 1, title: 'Test Dashboard' },
      openDashboardInNewTab: false,
      dashboardOrTemplate: 'template',
      assetOrAssetType: 1,
    },
  },
  selectedTitles: [],
  dbMeasureIdToName: {},
  assetMeasure: [],
  labelAndUnits: {},
  svgTexts: [],
  image: null,
  font: { color: '#000000', size: 12, weight: 'bolder' },
  margin: { b: 5, l: 5, r: 5, t: 5 },
  dashboard: null,
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  timeRange: 6,
  overrideDateSettings: false,
  startDate: new Date().getTime(),
  endDate: new Date().getTime(),
  assetTz: true,
  assetOrAssetType: null,
  dashboardOrTemplate: 'template',
  openDashboardInNewTab: false,
  uploadedImage: null,
  allowUpload: false,
  imgDashboard: {
    dashboard: null,
    openDashboardInNewTab: false,
    dashboardOrTemplate: 'template',
    assetOrAssetType: null,
  },
};

const defaultProps = {
  labelAndUnits: defaultLabelAndUnits,
  measureId: '1',
  setSettings: jest.fn(),
  settings: defaultSettings,
};

describe('MeasurementDashboard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByText('Template')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    it('should render radio group for dashboard or template selection', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      const templateRadio = screen.getByLabelText('Template');
      const dashboardRadio = screen.getByLabelText('Dashboard');

      expect(templateRadio).toBeInTheDocument();
      expect(dashboardRadio).toBeInTheDocument();
      expect(templateRadio).toBeChecked();
    });

    it('should render asset type autocomplete in template mode', () => {
      const settings = {
        ...defaultSettings,
        mode: 'template' as const,
      };

      renderWithProviders(
        <MeasurementDashboard
          {...defaultProps}
          settings={settings}
          labelAndUnits={{
            ...defaultLabelAndUnits,
            dashboardOrTemplate: 'template',
          }}
        />,
      );

      expect(screen.getByLabelText('Asset Type')).toBeInTheDocument();
    });

    it('should render asset autocomplete in dashboard mode with template selection', () => {
      const settings = {
        ...defaultSettings,
        mode: 'dashboard' as const,
      };

      renderWithProviders(
        <MeasurementDashboard
          {...defaultProps}
          settings={settings}
          labelAndUnits={{
            ...defaultLabelAndUnits,
            dashboardOrTemplate: 'template',
          }}
        />,
      );

      expect(screen.getByLabelText('Select Asset')).toBeInTheDocument();
    });

    it('should render dashboard/template autocomplete', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByLabelText('Link Dashboard Template')).toBeInTheDocument();
    });

    it('should render launch icon when dashboard is selected', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByTestId('LaunchIcon')).toBeInTheDocument();
    });

    it('should render open in new tab checkbox when dashboard is selected', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByLabelText('Open in New Tab')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should handle radio button change from template to dashboard', async () => {
      const setSettings = jest.fn();
      renderWithProviders(<MeasurementDashboard {...defaultProps} setSettings={setSettings} />);

      const dashboardRadio = screen.getByLabelText('Dashboard');
      await userEvent.click(dashboardRadio);

      expect(setSettings).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should handle asset type selection in template mode', async () => {
      const setSettings = jest.fn();
      const settings = {
        ...defaultSettings,
        mode: 'template' as const,
      };

      renderWithProviders(
        <MeasurementDashboard
          {...defaultProps}
          settings={settings}
          setSettings={setSettings}
          labelAndUnits={{
            ...defaultLabelAndUnits,
            dashboardOrTemplate: 'template',
          }}
        />,
      );

      const assetTypeField = screen.getByLabelText('Asset Type');
      fireEvent.mouseDown(assetTypeField);

      await waitFor(() => {
        const option = screen.getByText('Type 1');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle asset selection in dashboard mode', async () => {
      const setSettings = jest.fn();
      const settings = {
        ...defaultSettings,
        mode: 'dashboard' as const,
      };

      renderWithProviders(
        <MeasurementDashboard
          {...defaultProps}
          settings={settings}
          setSettings={setSettings}
          labelAndUnits={{
            ...defaultLabelAndUnits,
            dashboardOrTemplate: 'template',
          }}
        />,
      );

      const assetField = screen.getByLabelText('Select Asset');
      fireEvent.mouseDown(assetField);

      await waitFor(() => {
        const option = screen.getByText('Asset 1');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle dashboard/template selection', async () => {
      const setSettings = jest.fn();
      renderWithProviders(<MeasurementDashboard {...defaultProps} setSettings={setSettings} />);

      const dashboardField = screen.getByLabelText('Link Dashboard Template');
      fireEvent.mouseDown(dashboardField);

      await waitFor(() => {
        const option = screen.getByText('Template 1');
        fireEvent.click(option);
      });

      expect(setSettings).toHaveBeenCalled();
    });

    it('should handle open in new tab checkbox change', async () => {
      const setSettings = jest.fn();
      renderWithProviders(<MeasurementDashboard {...defaultProps} setSettings={setSettings} />);

      const checkbox = screen.getByLabelText('Open in New Tab');
      await userEvent.click(checkbox);

      expect(setSettings).toHaveBeenCalled();
    });
  });

  describe('Redux Integration', () => {
    it('should dispatch dashboard navigation actions when opening dashboard', async () => {
      const store = createTestStore();

      renderWithProviders(<MeasurementDashboard {...defaultProps} />, { store });

      const launchButton = screen.getByTestId('LaunchIcon').closest('button');
      await userEvent.click(launchButton!);

      expect(mockRouter.push).toHaveBeenCalledWith('/customer/1/dashboard/1');
    });

    it('should update measurement dashboard settings in Redux state', async () => {
      const setSettings = jest.fn();
      const store = createTestStore();

      renderWithProviders(<MeasurementDashboard {...defaultProps} setSettings={setSettings} />, {
        store,
      });

      const dashboardRadio = screen.getByLabelText('Dashboard');
      await userEvent.click(dashboardRadio);

      expect(setSettings).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('API Integration', () => {
    it('should handle loading states for dashboards', () => {
      const mockUseGetDashboardByCustomerIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByCustomerIdQuery;
      mockUseGetDashboardByCustomerIdQuery.mockReturnValue({
        data: null,
        isLoading: true,
      });

      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should handle loading states for dashboard templates', () => {
      const mockUseGetDashboardTemplatesQuery =
        require('~/redux/api/dashboardTemplate').useGetDashboardTemplatesQuery;
      mockUseGetDashboardTemplatesQuery.mockReturnValue({
        data: null,
        isLoading: true,
      });

      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should handle API errors gracefully', () => {
      const mockUseGetDashboardByCustomerIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByCustomerIdQuery;
      mockUseGetDashboardByCustomerIdQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: { message: 'API Error' },
      });

      expect(() => {
        renderWithProviders(<MeasurementDashboard {...defaultProps} />);
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing customer data gracefully', () => {
      const store = createTestStore({ customer: null });

      expect(() => {
        renderWithProviders(<MeasurementDashboard {...defaultProps} />, { store });
      }).not.toThrow();
    });

    it('should handle empty dashboard list', () => {
      const mockUseGetDashboardByCustomerIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByCustomerIdQuery;
      mockUseGetDashboardByCustomerIdQuery.mockReturnValue({
        data: { items: [] },
        isLoading: false,
      });

      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByLabelText('Link Dashboard Template')).toBeInTheDocument();
    });

    it('should handle missing measureId gracefully', () => {
      expect(() => {
        renderWithProviders(<MeasurementDashboard {...defaultProps} measureId="" />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for form controls', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      expect(screen.getByLabelText('Template')).toBeInTheDocument();
      expect(screen.getByLabelText('Dashboard')).toBeInTheDocument();
      expect(screen.getByLabelText('Link Dashboard Template')).toBeInTheDocument();
      expect(screen.getByLabelText('Open in New Tab')).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      const templateRadio = screen.getByLabelText('Template');
      templateRadio.focus();

      expect(templateRadio).toHaveFocus();
    });

    it('should have proper tooltip for launch button', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      const launchButton = screen.getByTestId('LaunchIcon').closest('button');
      expect(launchButton).toHaveAttribute('aria-describedby');
    });

    it('should have proper role attributes for interactive elements', () => {
      renderWithProviders(<MeasurementDashboard {...defaultProps} />);

      const radioGroup = screen.getByRole('radiogroup');
      expect(radioGroup).toBeInTheDocument();

      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);
    });
  });
});

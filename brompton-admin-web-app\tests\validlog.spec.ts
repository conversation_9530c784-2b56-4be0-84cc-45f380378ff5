import { test, expect } from '@playwright/test';

test('Positive Login Test - Valid Credentials', async ({ page }) => {
  const baseUrl = 'https://dev.pivotol.ai/login';
  const validUsername = 'test';
  const validPassword = 'Br0mpt0n!0T';

  console.log('Starting Positive Login Test');

  await page.goto(baseUrl);
  console.log(`Navigated to: ${baseUrl}`);

  await page.getByLabel('Username *').fill(validUsername);
  await page.getByLabel('Password *').fill(validPassword);
  console.log('Filled in valid credentials');

  await page.getByRole('button', { name: 'Log in' }).click();
  console.log('Clicked login button');

  // Wait for dashboard header to appear
  const dashboardHeader = page.getByRole('heading', { name: 'Dashboard' });
  await expect(dashboardHeader).toBeVisible();
  console.log('Assertion Passed: Dashboard is visible after login');

  await page.screenshot({ path: 'positive-login-test.png' });

  console.log('Positive Login Test completed');
});

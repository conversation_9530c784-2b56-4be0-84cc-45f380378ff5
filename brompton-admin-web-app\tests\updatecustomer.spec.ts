import { test, expect } from '@playwright/test';
test('updateCustomer', async ({ page }) => {
    // Open Login Page
    await page.goto('https://dev.pivotol.ai/login');

    // Login Flow
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    //click on D
    await page.getByRole('button', { name: 'D', exact: true }).click();

    // Ensure "Customer Management" is loaded before clicking
    await page.waitForSelector('text=Customer Management', { timeout: 5000 });
    await page.getByText('Customer Management').click();

    // Handle "Proceed" button conditionally
    const proceedButton = page.locator('button:has-text("Proceed")');
    if (await proceedButton.isVisible()) {
        console.log('Proceed button is visible, clicking it.');
        await proceedButton.click();
    } else {
        console.log('Proceed button is NOT visible, skipping this step.');
    }
    await page.waitForTimeout(5000);
   // await page.locator('#combo-box-demo').click();
   
   await page.getByLabel('Customer').click();
  await page.getByRole('option', { name: 'san' }).click();
  await page.locator('div:nth-child(7) > div > .MuiButtonBase-root').click();
  await page.getByLabel('Name *').click();
  await page.getByLabel('Name *').fill('san');
  await page.getByLabel('Address *').click();
  await page.getByLabel('Address *').fill('seaviews');

   // Ensure File Upload Works Correctly
   const fileInput = page.locator('input[type="file"]');
   if (await fileInput.isVisible()) {
       await fileInput.setInputFiles('C:/Users/<USER>/OneDrive/Pictures/logo/sm_5afa55dd1cd4e.jpg');
   } else {
       console.log('File input not found, skipping file upload.');
   }

   await page.getByRole('button', { name: 'Update' }).click();
   await page.waitForTimeout(1000);
  // Close Page
  await page.close();
   });
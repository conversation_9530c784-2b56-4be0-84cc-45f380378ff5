import { test, expect, Page } from '@playwright/test';

// ---------- Reusable Functions ----------

async function login(page: Page, username: string, password: string) {
  await page.goto('https://dev.pivotol.ai/login');
  await page.getByLabel('Username *').fill(username);
  await page.getByLabel('Password *').fill(password);
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000);
}

async function navigateToDashboard(page: Page) {
  await page.getByRole('button', { name: 'Dashboards' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage' }).click();
  await page.waitForTimeout(10000);
  await page.getByRole('button', { name: 'New Dashboard' }).click();
}

async function dragAndConfigureHeatmapWidget(page: Page, assetName: string, measurementName: string) {
  // Open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  // Drag Heatmap widget
  const heatmapWidget = page.locator('span.MuiListItemText-primary', { hasText: 'Heatmap' });
  const layoutArea = page.locator('.react-grid-layout.layout');
  await heatmapWidget.dragTo(layoutArea);
  await page.waitForTimeout(2000);

  // Open widget options
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
  await parentElement.hover();

  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  // Click settings menu
  await page.locator('#widget-settings-menu-1 ul li').nth(3).click(); // li[4] (index starts at 0)
  await page.waitForTimeout(5000);

  // Configure Asset and Measurement
  await page.locator('#asset-autocomplete-heatmap-series').click();
  await page.getByRole('option', { name: assetName }).click();

  await page.getByRole('combobox', { name: 'Select Measurement' }).click();
  await page.getByRole('option', { name: measurementName }).click();

  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForTimeout(3000);
}

async function configureTimeRange(page: Page, timeRangeLabel: string = 'Last 90 days') {
  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: timeRangeLabel }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.waitForTimeout(5000);
}

// ---------- Test ----------

test('drag and configure Heatmap widget', async ({ page }) => {
  await login(page, 'test', 'Br0mpt0n!0T');
  await navigateToDashboard(page);

  await dragAndConfigureHeatmapWidget(page, 
    'BromptonSimulations > Camden Headquarter > BT-001 > BT-001-1', 
    'Jt-'
  );

  await configureTimeRange(page, 'Last 90 days');

  await page.close();
});

import { test, expect } from '@playwright/test';

test('dashboardtemplate', async ({ page }) => {
  // Open the URL
  await page.goto('https://dev.pivotol.ai/login');

  // Fill in Username and Password
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');

  // Click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000); // Wait for the login to process

  // Click on Add Dashboards
  await page.getByRole('button', { name: 'Dashboards' }).click();
  await page.waitForTimeout(3000)
  await page.getByRole('menuitem', { name: 'Templates' }).click();
  await page.getByRole('button', { name: 'New Dashboard Template' }).click();
  
  // using explicit wait Now you can interact with the asset type element safely
  await page.waitForSelector('input[aria-autocomplete="list"]', {
    state: 'visible',
    timeout: 10000,
  });

  // Interact with Asset Type autocomplete
  const assetType = page.locator('input[aria-autocomplete="list"]').first();
  await assetType.click();
  await assetType.fill('Meter > BTU');
  await page.waitForTimeout(1000); // Wait for options to appear
  await page.getByRole('option', { name: 'Meter > BTU (17)' }).click();

  // Interact with Asset Template autocomplete
  const assetTemplate = page.locator('input[aria-autocomplete="list"]').nth(1);
  await assetTemplate.click();
  await assetTemplate.fill('m009 - manfac');
  await page.waitForTimeout(1000); // Wait for options to appear
  await page.getByRole('option', { name: 'm009 - manfac' }).click();

  // Click on Widgets icon to open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  // Drag and drop the table widget into the layout area
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);

  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);
  // First, locate the parent element you need to hover over to reveal the icon
  const parentElement = page.locator('.MuiBox-root.css-1j35o1p'); // Adjust selector if necessary

  // Hover over the parent element to make the icon visible
  await parentElement.hover();

  // Now, locate the 'MoreVertIcon' options button, which should be visible after hovering
  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');

  // Wait for the icon to become visible and then click it
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  //await page.waitForTimeout(4000);
  await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
  await page.waitForTimeout(6000);

  // Select Measure
  await page.getByRole('combobox').first().click();
  await page.getByRole('option', { name: 'PHASEA:APPARENT_ENERGY' }).click();
  // Click a neutral part of the UI (like the "Link" section title)
  // Send Tab key to shift focus
  await page.keyboard.press('Tab');
  await page.getByRole('button', { name: 'Update' }).click();
  await page.locator('button:has-text("Save")').click({ force: true });


  await page.waitForTimeout(3000);
  //await page.click('.MuiButtonBase-root.MuiButton-containedPrimary');
  await page.getByRole('textbox', { name: 'Dashboard Title' }).click();
  await page.getByRole('textbox', { name: 'Dashboard Title' }).fill('new temp');
  await page.getByRole('checkbox', { name: 'Save as Global Dashboard' }).check();
  await page.getByRole('button', { name: 'Save' }).click();

  // Wait for final actions and close the page
  await page.waitForTimeout(6000);
  await page.close();
});

import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import DashboardsCrumb from '../DashboardCrumbs';

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

const mockPush = jest.fn();
const mockDispatch = jest.fn();

describe('DashboardsCrumb Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });

    // Default useSelector mock for most tests
    (useSelector as jest.Mock).mockImplementation((selectorFn: any) => {
      const fakeState = {
        dashboard: {
          crumbs: [
            { dashboardTitle: 'Home', dashboardId: 1 },
            { dashboardTitle: 'Details', dashboardId: 2 },
          ],
        },
        customer: {
          activeCustomer: { id: 101 },
        },
      };
      return selectorFn(fakeState);
    });
  });

  it('should have proper semantic structure', () => {
    render(<DashboardsCrumb />);

    const list = screen.getByRole('list');
    expect(list).toBeInTheDocument();
  });

  it('should show snackbar if template fetch is triggered', async () => {
    (useSelector as jest.Mock).mockImplementation((selectorFn: any) => {
      const fakeState = {
        dashboard: {
          crumbs: [
            {
              dashboardTitle: 'Template View',
              dashboardId: -2,
              assetId: 10,
              assetType: 'Chiller',
              templateId: 123,
            },
          ],
        },
        customer: {
          activeCustomer: { id: 101 },
        },
      };
      return selectorFn(fakeState);
    });

    render(<DashboardsCrumb />);
  });

  it('should clean up popstate listener on unmount', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');

    const { unmount } = render(<DashboardsCrumb />);
    unmount();

    expect(addEventListenerSpy).toHaveBeenCalledWith('popstate', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('popstate', expect.any(Function));
  });
});

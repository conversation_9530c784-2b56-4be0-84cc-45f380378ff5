// npm test -- --testPathPattern=DashboardCrumbs.test.tsx --verbose --watchAll=false
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import React from 'react';
import { Provider } from 'react-redux';

import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import DashboardCrumbs from '../DashboardCrumbs';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock role permission hook
jest.mock('~/hooks/useRolePermission', () => ({
  useRolePermission: jest.fn(() => ({
    hasDashboardPermission: jest.fn(() => true),
  })),
  Role: {
    POWER_USER: 'POWER_USER',
  },
}));

// Mock API hooks
jest.mock('~/redux/api/dashboardApi', () => ({
  useGetDashboardByCustomerIdQuery: jest.fn(() => ({
    data: {
      items: [
        { id: 1, title: 'Dashboard 1', description: 'Test Dashboard 1' },
        { id: 2, title: 'Dashboard 2', description: 'Test Dashboard 2' },
      ],
    },
    isLoading: false,
    isSuccess: true,
  })),
  useGetDashboardByIdQuery: jest.fn(() => ({
    data: {
      id: 1,
      title: 'Current Dashboard',
      description: 'Current Dashboard Description',
    },
    isLoading: false,
    isSuccess: true,
  })),
}));

jest.mock('~/redux/api/dashboardTemplate', () => ({
  useGetDashboardTemplatesQuery: jest.fn(() => ({
    data: {
      items: [
        { id: 1, title: 'Template 1', description: 'Test Template 1' },
        { id: 2, title: 'Template 2', description: 'Test Template 2' },
      ],
    },
    isLoading: false,
    isSuccess: true,
  })),
}));

// Mock router object
const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: { customerId: '1', dashboardId: '1' },
  asPath: '/customer/1/dashboard/1',
  route: '/customer/[customerId]/dashboard/[dashboardId]',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
  basePath: '',
};

// Helper function to create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        currentDashboardId: 1,
        customer: {
          id: 1,
          name: 'Test Customer',
          nameId: 'test-customer',
          address: 'Test Address',
          logo: '',
          enabled: true,
        },
        ...initialState,
      },
    },
  });
};

const mockTheme = createTheme();

// Helper function to render component with providers
const renderWithProviders = (
  component: React.ReactElement,
  { store = createTestStore(), ...renderOptions } = {},
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>{children}</ThemeProvider>
    </Provider>
  );

  return {
    store,
    ...render(component, { wrapper: Wrapper, ...renderOptions }),
  };
};

describe('DashboardCrumbs Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      renderWithProviders(<DashboardCrumbs />);

      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('should render breadcrumb navigation', () => {
      renderWithProviders(<DashboardCrumbs />);

      const breadcrumbs = screen.getByRole('navigation');
      expect(breadcrumbs).toHaveAttribute('aria-label', 'breadcrumb');
    });

    it('should render customer name in breadcrumb', () => {
      renderWithProviders(<DashboardCrumbs />);

      expect(screen.getByText('Test Customer')).toBeInTheDocument();
    });

    it('should render dashboard title in breadcrumb', () => {
      renderWithProviders(<DashboardCrumbs />);

      expect(screen.getByText('Current Dashboard')).toBeInTheDocument();
    });

    it('should render home icon link', () => {
      renderWithProviders(<DashboardCrumbs />);

      const homeIcon = screen.getByTestId('HomeIcon');
      expect(homeIcon).toBeInTheDocument();
    });

    it('should render dashboard selector dropdown', () => {
      renderWithProviders(<DashboardCrumbs />);

      const dashboardSelector = screen.getByRole('combobox');
      expect(dashboardSelector).toBeInTheDocument();
    });

    it('should show loading state when dashboard data is loading', () => {
      const mockUseGetDashboardByIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByIdQuery;
      mockUseGetDashboardByIdQuery.mockReturnValue({
        data: null,
        isLoading: true,
        isSuccess: false,
      });

      renderWithProviders(<DashboardCrumbs />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should navigate to home when home icon is clicked', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const homeIcon = screen.getByTestId('HomeIcon').closest('a');
      await userEvent.click(homeIcon!);

      expect(mockRouter.push).toHaveBeenCalledWith('/customer/1');
    });

    it('should navigate to customer dashboard list when customer name is clicked', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const customerLink = screen.getByText('Test Customer').closest('a');
      await userEvent.click(customerLink!);

      expect(mockRouter.push).toHaveBeenCalledWith('/customer/1');
    });

    it('should open dashboard selector dropdown when clicked', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const dashboardSelector = screen.getByRole('combobox');
      fireEvent.mouseDown(dashboardSelector);

      await waitFor(() => {
        expect(screen.getByText('Dashboard 1')).toBeInTheDocument();
        expect(screen.getByText('Dashboard 2')).toBeInTheDocument();
      });
    });

    it('should navigate to selected dashboard when option is chosen', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const dashboardSelector = screen.getByRole('combobox');
      fireEvent.mouseDown(dashboardSelector);

      await waitFor(() => {
        const option = screen.getByText('Dashboard 2');
        fireEvent.click(option);
      });

      expect(mockRouter.push).toHaveBeenCalledWith('/customer/1/dashboard/2');
    });

    it('should handle keyboard navigation in dropdown', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const dashboardSelector = screen.getByRole('combobox');
      dashboardSelector.focus();

      fireEvent.keyDown(dashboardSelector, { key: 'ArrowDown' });

      await waitFor(() => {
        expect(screen.getByText('Dashboard 1')).toBeInTheDocument();
      });
    });
  });

  describe('Redux Integration', () => {
    it('should use current dashboard ID from Redux state', () => {
      const store = createTestStore({ currentDashboardId: 2 });

      renderWithProviders(<DashboardCrumbs />, { store });

      const mockUseGetDashboardByIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByIdQuery;
      expect(mockUseGetDashboardByIdQuery).toHaveBeenCalledWith(2);
    });

    it('should use customer data from Redux state', () => {
      const customCustomer = {
        id: 2,
        name: 'Custom Customer',
        nameId: 'custom-customer',
        address: 'Custom Address',
        logo: '',
      };
      const store = createTestStore({ customer: customCustomer });

      renderWithProviders(<DashboardCrumbs />, { store });

      expect(screen.getByText('Custom Customer')).toBeInTheDocument();
    });

    it('should handle missing customer data gracefully', () => {
      const store = createTestStore({ customer: null });

      expect(() => {
        renderWithProviders(<DashboardCrumbs />, { store });
      }).not.toThrow();
    });
  });

  describe('API Integration', () => {
    it('should fetch dashboard list for customer', () => {
      renderWithProviders(<DashboardCrumbs />);

      const mockUseGetDashboardByCustomerIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByCustomerIdQuery;
      expect(mockUseGetDashboardByCustomerIdQuery).toHaveBeenCalledWith(1);
    });

    it('should fetch current dashboard details', () => {
      renderWithProviders(<DashboardCrumbs />);

      const mockUseGetDashboardByIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByIdQuery;
      expect(mockUseGetDashboardByIdQuery).toHaveBeenCalledWith(1);
    });

    it('should handle API errors gracefully', () => {
      const mockUseGetDashboardByIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByIdQuery;
      mockUseGetDashboardByIdQuery.mockReturnValue({
        data: null,
        isLoading: false,
        isSuccess: false,
        error: { message: 'API Error' },
      });

      expect(() => {
        renderWithProviders(<DashboardCrumbs />);
      }).not.toThrow();
    });

    it('should handle empty dashboard list', () => {
      const mockUseGetDashboardByCustomerIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByCustomerIdQuery;
      mockUseGetDashboardByCustomerIdQuery.mockReturnValue({
        data: { items: [] },
        isLoading: false,
        isSuccess: true,
      });

      renderWithProviders(<DashboardCrumbs />);

      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing router query parameters', () => {
      const routerWithoutQuery = {
        ...mockRouter,
        query: {},
      };
      (useRouter as jest.Mock).mockReturnValue(routerWithoutQuery);

      expect(() => {
        renderWithProviders(<DashboardCrumbs />);
      }).not.toThrow();
    });

    it('should handle invalid dashboard ID in URL', () => {
      const routerWithInvalidId = {
        ...mockRouter,
        query: { customerId: '1', dashboardId: 'invalid' },
      };
      (useRouter as jest.Mock).mockReturnValue(routerWithInvalidId);

      expect(() => {
        renderWithProviders(<DashboardCrumbs />);
      }).not.toThrow();
    });

    it('should handle network errors gracefully', () => {
      const mockUseGetDashboardByCustomerIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByCustomerIdQuery;
      mockUseGetDashboardByCustomerIdQuery.mockReturnValue({
        data: null,
        isLoading: false,
        isSuccess: false,
        error: { status: 500, message: 'Internal Server Error' },
      });

      expect(() => {
        renderWithProviders(<DashboardCrumbs />);
      }).not.toThrow();
    });

    it('should handle missing dashboard data', () => {
      const mockUseGetDashboardByIdQuery =
        require('~/redux/api/dashboardApi').useGetDashboardByIdQuery;
      mockUseGetDashboardByIdQuery.mockReturnValue({
        data: null,
        isLoading: false,
        isSuccess: true,
      });

      expect(() => {
        renderWithProviders(<DashboardCrumbs />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for navigation elements', () => {
      renderWithProviders(<DashboardCrumbs />);

      const navigation = screen.getByRole('navigation');
      expect(navigation).toHaveAttribute('aria-label', 'breadcrumb');
    });

    it('should have proper ARIA labels for interactive elements', () => {
      renderWithProviders(<DashboardCrumbs />);

      const homeLink = screen.getByTestId('HomeIcon').closest('a');
      expect(homeLink).toHaveAttribute('aria-label');

      const dashboardSelector = screen.getByRole('combobox');
      expect(dashboardSelector).toHaveAttribute('aria-label');
    });

    it('should support keyboard navigation for breadcrumb links', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const homeLink = screen.getByTestId('HomeIcon').closest('a');
      homeLink!.focus();

      expect(homeLink).toHaveFocus();

      fireEvent.keyDown(homeLink!, { key: 'Tab' });

      const customerLink = screen.getByText('Test Customer').closest('a');
      expect(customerLink).toBeInTheDocument();
    });

    it('should support keyboard navigation for dashboard selector', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const dashboardSelector = screen.getByRole('combobox');
      dashboardSelector.focus();

      expect(dashboardSelector).toHaveFocus();

      fireEvent.keyDown(dashboardSelector, { key: 'Enter' });

      await waitFor(() => {
        expect(screen.getByText('Dashboard 1')).toBeInTheDocument();
      });
    });

    it('should have proper semantic structure', () => {
      renderWithProviders(<DashboardCrumbs />);

      const breadcrumbList = screen.getByRole('list');
      expect(breadcrumbList).toBeInTheDocument();

      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems.length).toBeGreaterThan(0);
    });

    it('should have proper focus management', async () => {
      renderWithProviders(<DashboardCrumbs />);

      const dashboardSelector = screen.getByRole('combobox');
      fireEvent.mouseDown(dashboardSelector);

      await waitFor(() => {
        const firstOption = screen.getByText('Dashboard 1');
        expect(firstOption).toBeInTheDocument();
      });

      fireEvent.keyDown(dashboardSelector, { key: 'ArrowDown' });
      fireEvent.keyDown(dashboardSelector, { key: 'Enter' });

      expect(mockRouter.push).toHaveBeenCalled();
    });

    it('should provide screen reader friendly text', () => {
      renderWithProviders(<DashboardCrumbs />);

      // Check for visually hidden text that helps screen readers
      const navigation = screen.getByRole('navigation');
      expect(navigation).toBeInTheDocument();

      // Verify breadcrumb separators are accessible
      const breadcrumbItems = screen.getAllByRole('listitem');
      expect(breadcrumbItems.length).toBeGreaterThan(1);
    });
  });
});

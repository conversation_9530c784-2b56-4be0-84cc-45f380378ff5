// npm test -- --testPathPattern=DashboardWidgetSettingsDialog.test.tsx --verbose --watchAll=false
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import React from 'react';
import { Provider } from 'react-redux';

import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { DashboardWidget } from '~/types/widgets';
import DashboardWidgetSettingsDialog from '../DashboardWidgetSettingsDialog';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock role permission hook
jest.mock('~/hooks/useRolePermission', () => ({
  useRolePermission: jest.fn(() => ({
    hasDashboardPermission: jest.fn(() => true),
  })),
  Role: {
    POWER_USER: 'POWER_USER',
  },
}));

// Mock API hooks
jest.mock('~/redux/api/dashboardApi', () => ({
  useUpdateDashboardMutation: jest.fn(() => [
    jest.fn().mockResolvedValue({ data: { success: true } }),
    { isLoading: false, error: null },
  ]),
}));

jest.mock('~/redux/api/dashboardTemplate', () => ({
  useUpdateDashboardTemplateMutation: jest.fn(() => [
    jest.fn().mockResolvedValue({ data: { success: true } }),
    { isLoading: false, error: null },
  ]),
}));

// Mock utils
jest.mock('~/utils/utils', () => ({
  generateUniqueId: jest.fn(() => 'unique-id-123'),
  deepClone: jest.fn((obj) => JSON.parse(JSON.stringify(obj))),
}));

// Mock router object
const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: { customerId: '1', dashboardId: '1' },
  asPath: '',
  route: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
  basePath: '',
};

// Helper function to create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        currentDashboardId: 1,
        customer: {
          id: 1,
          name: 'Test Customer',
          nameId: 'test-customer',
          address: 'Test Address',
          logo: '',
          enabled: true,
        },
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: {
            widgetLayout: [],
            widgets: [],
          },
          mobile: {
            widgetLayout: [],
            widgets: [],
          },
        },
        ...initialState,
      },
    },
  });
};

const mockTheme = createTheme();

// Helper function to render component with providers
const renderWithProviders = (
  component: React.ReactElement,
  { store = createTestStore(), ...renderOptions } = {},
) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <Provider store={store}>
      <ThemeProvider theme={mockTheme}>{children}</ThemeProvider>
    </Provider>
  );

  return {
    store,
    ...render(component, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Default props for the component
const defaultSettings: DashboardWidget = {
  // WidgetCommonSettings
  title: {
    value: 'Test Widget',
    isVisible: true,
    color: '#000000',
    fontSize: 14,
    fontWeight: 'normal',
  },
  isRealTime: false,
  retainPeriod: 60,
  refreshInterval: 5,
  isValid: true,
  isDirty: false,
  mode: 'dashboard',
  isChildWidget: false,
  dashboardOrTemplate: 'template',
  assetOrAssetType: null,
  dashboard: null,
  openDashboardInNewTab: false,

  // DataWidget
  overrideAssetTz: false,
  overrideAssetTzValue: false,
  aggBy: 1,
  samplePeriod: 2,
  overrideGlobalSettings: false,
  isRelativeToGlboalEndTime: false,
  timeRange: 6,
  startDate: Date.now(),
  endDate: Date.now(),
  globalSamplePeriod: false,

  // DashboardWidget specific
  assetMeasure: [],
  dbMeasureIdToName: {},
  selectedTitles: [],
  assetTypes: [],
  measurementTypes: [],
  assetId: '',
  metricToMeasurementMap: {},
  dashboardTemplateData: null,
  assetTypeMetrics: [],
  assetOption: {
    label: 'Test Asset',
    id: 1,
  },
  dashboardTemplateOption: {
    label: 'Test Template',
    id: 1,
  },
};

const defaultProps = {
  settings: defaultSettings,
  handleSettingsChange: jest.fn() as (value: ((prevState: DashboardWidget) => DashboardWidget) | DashboardWidget) => void,
};

describe('DashboardWidgetSettingsDialog Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('should render dialog title', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByText('Widget Settings')).toBeInTheDocument();
    });

    it('should render widget title input', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByLabelText('Widget Title')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Widget')).toBeInTheDocument();
    });

    it('should render tabs for different settings sections', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getByText('General')).toBeInTheDocument();
      expect(screen.getByText('Data')).toBeInTheDocument();
    });

    it('should render action buttons', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByText('Cancel')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
    });

    it('should render close button', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const closeButton = screen.getByLabelText('close');
      expect(closeButton).toBeInTheDocument();
    });

    it('should not render when open is false', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} open={false} />);

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('should render different content for template mode', () => {
      const props = {
        ...defaultProps,
        mode: 'template' as const,
        widget: {
          ...defaultWidget,
          settings: {
            ...defaultWidget.settings,
            mode: 'template' as const,
          },
        },
      };

      renderWithProviders(<DashboardWidgetSettingsDialog {...props} />);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should handle dialog close via close button', async () => {
      const onClose = jest.fn();
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} onClose={onClose} />);

      const closeButton = screen.getByLabelText('close');
      await userEvent.click(closeButton);

      expect(onClose).toHaveBeenCalled();
    });

    it('should handle dialog close via cancel button', async () => {
      const onClose = jest.fn();
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} onClose={onClose} />);

      const cancelButton = screen.getByText('Cancel');
      await userEvent.click(cancelButton);

      expect(onClose).toHaveBeenCalled();
    });

    it('should handle widget title change', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const titleInput = screen.getByLabelText('Widget Title');
      await userEvent.clear(titleInput);
      await userEvent.type(titleInput, 'New Widget Title');

      expect(titleInput).toHaveValue('New Widget Title');
    });

    it('should handle tab navigation', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const dataTab = screen.getByText('Data');
      await userEvent.click(dataTab);

      expect(dataTab).toHaveAttribute('aria-selected', 'true');
    });

    it('should handle save button click', async () => {
      const mockUpdateDashboard = jest.fn().mockResolvedValue({ data: { success: true } });
      const mockUseUpdateDashboardMutation =
        require('~/redux/api/dashboardApi').useUpdateDashboardMutation;
      mockUseUpdateDashboardMutation.mockReturnValue([mockUpdateDashboard, { isLoading: false }]);

      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      expect(mockUpdateDashboard).toHaveBeenCalled();
    });

    it('should handle keyboard navigation', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const dialog = screen.getByRole('dialog');
      expect(dialog).toBeInTheDocument();

      // Test Escape key to close dialog
      fireEvent.keyDown(dialog, { key: 'Escape' });

      expect(defaultProps.onClose).toHaveBeenCalled();
    });
  });

  describe('Redux Integration', () => {
    it('should dispatch dashboard update action on save', async () => {
      const store = createTestStore();
      const mockUpdateDashboard = jest.fn().mockResolvedValue({ data: { success: true } });
      const mockUseUpdateDashboardMutation =
        require('~/redux/api/dashboardApi').useUpdateDashboardMutation;
      mockUseUpdateDashboardMutation.mockReturnValue([mockUpdateDashboard, { isLoading: false }]);

      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />, { store });

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      expect(mockUpdateDashboard).toHaveBeenCalled();
    });

    it('should dispatch template update action on save in template mode', async () => {
      const store = createTestStore();
      const mockUpdateTemplate = jest.fn().mockResolvedValue({ data: { success: true } });
      const mockUseUpdateDashboardTemplateMutation =
        require('~/redux/api/dashboardTemplate').useUpdateDashboardTemplateMutation;
      mockUseUpdateDashboardTemplateMutation.mockReturnValue([
        mockUpdateTemplate,
        { isLoading: false },
      ]);

      const props = {
        ...defaultProps,
        mode: 'template' as const,
        widget: {
          ...defaultWidget,
          settings: {
            ...defaultWidget.settings,
            mode: 'template' as const,
          },
        },
      };

      renderWithProviders(<DashboardWidgetSettingsDialog {...props} />, { store });

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      expect(mockUpdateTemplate).toHaveBeenCalled();
    });

    it('should use responsive layout data from Redux state', () => {
      const store = createTestStore({
        desktopMobile: 1,
        responsiveLayouts: {
          desktop: { widgetLayout: [], widgets: [] },
          mobile: {
            widgetLayout: [{ i: 'widget-1', x: 0, y: 0, w: 4, h: 4 }],
            widgets: [defaultWidget],
          },
        },
      });

      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />, { store });

      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('API Integration', () => {
    it('should handle loading state during save operation', async () => {
      const mockUpdateDashboard = jest.fn().mockResolvedValue({ data: { success: true } });
      const mockUseUpdateDashboardMutation =
        require('~/redux/api/dashboardApi').useUpdateDashboardMutation;
      mockUseUpdateDashboardMutation.mockReturnValue([mockUpdateDashboard, { isLoading: true }]);

      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();
    });

    it('should handle API errors during save operation', async () => {
      const mockUpdateDashboard = jest.fn().mockRejectedValue(new Error('API Error'));
      const mockUseUpdateDashboardMutation =
        require('~/redux/api/dashboardApi').useUpdateDashboardMutation;
      mockUseUpdateDashboardMutation.mockReturnValue([
        mockUpdateDashboard,
        { isLoading: false, error: { message: 'API Error' } },
      ]);

      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      // Component should handle error gracefully
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('should show success message after successful save', async () => {
      const mockUpdateDashboard = jest.fn().mockResolvedValue({ data: { success: true } });
      const mockUseUpdateDashboardMutation =
        require('~/redux/api/dashboardApi').useUpdateDashboardMutation;
      mockUseUpdateDashboardMutation.mockReturnValue([mockUpdateDashboard, { isLoading: false }]);

      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      await waitFor(() => {
        expect(mockUpdateDashboard).toHaveBeenCalled();
      });
    });

    it('should handle network connectivity issues', async () => {
      const mockUpdateDashboard = jest.fn().mockRejectedValue(new Error('Network Error'));
      const mockUseUpdateDashboardMutation =
        require('~/redux/api/dashboardApi').useUpdateDashboardMutation;
      mockUseUpdateDashboardMutation.mockReturnValue([
        mockUpdateDashboard,
        { isLoading: false, error: { status: 'FETCH_ERROR' } },
      ]);

      expect(() => {
        renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing widget data gracefully', () => {
      const props = {
        ...defaultProps,
        widget: null,
      };

      expect(() => {
        renderWithProviders(<DashboardWidgetSettingsDialog {...props} />);
      }).not.toThrow();
    });

    it('should handle invalid widget settings', () => {
      const props = {
        ...defaultProps,
        widget: {
          ...defaultWidget,
          settings: null,
        },
      };

      expect(() => {
        renderWithProviders(<DashboardWidgetSettingsDialog {...props} />);
      }).not.toThrow();
    });

    it('should handle missing customer data', () => {
      const store = createTestStore({ customer: null });

      expect(() => {
        renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />, { store });
      }).not.toThrow();
    });

    it('should validate required fields before save', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const titleInput = screen.getByLabelText('Widget Title');
      await userEvent.clear(titleInput);

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      // Should show validation error or prevent save
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('should handle form validation errors', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const titleInput = screen.getByLabelText('Widget Title');
      await userEvent.clear(titleInput);
      await userEvent.type(titleInput, ''); // Empty title

      fireEvent.blur(titleInput);

      // Should show validation message
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for dialog elements', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-labelledby');
      expect(dialog).toHaveAttribute('aria-describedby');
    });

    it('should have proper ARIA labels for form controls', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByLabelText('Widget Title')).toBeInTheDocument();

      const tabs = screen.getAllByRole('tab');
      tabs.forEach((tab) => {
        expect(tab).toHaveAttribute('aria-selected');
      });
    });

    it('should support keyboard navigation within dialog', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const titleInput = screen.getByLabelText('Widget Title');
      titleInput.focus();

      expect(titleInput).toHaveFocus();

      fireEvent.keyDown(titleInput, { key: 'Tab' });

      // Should move focus to next focusable element
      const nextElement = screen.getByText('General');
      expect(nextElement).toBeInTheDocument();
    });

    it('should trap focus within dialog', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const dialog = screen.getByRole('dialog');
      expect(dialog).toBeInTheDocument();

      // Focus should be trapped within dialog
      const titleInput = screen.getByLabelText('Widget Title');
      titleInput.focus();

      expect(titleInput).toHaveFocus();
    });

    it('should have proper role attributes for interactive elements', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getAllByRole('tab')).toHaveLength(2);
      expect(screen.getAllByRole('button')).toHaveLength(3); // Close, Cancel, Save
    });

    it('should announce dialog state changes to screen readers', async () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const dataTab = screen.getByText('Data');
      await userEvent.click(dataTab);

      expect(dataTab).toHaveAttribute('aria-selected', 'true');

      const generalTab = screen.getByText('General');
      expect(generalTab).toHaveAttribute('aria-selected', 'false');
    });

    it('should have proper heading structure', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const heading = screen.getByText('Widget Settings');
      expect(heading).toBeInTheDocument();
      expect(heading.tagName).toBe('H2');
    });

    it('should support screen reader navigation', () => {
      renderWithProviders(<DashboardWidgetSettingsDialog {...defaultProps} />);

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');

      const closeButton = screen.getByLabelText('close');
      expect(closeButton).toHaveAttribute('aria-label', 'close');
    });
  });
});

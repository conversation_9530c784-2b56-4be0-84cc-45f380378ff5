import { test, expect } from '@playwright/test';

test('Positive Test - Navigate to Analytics and Apply Filters', async ({ page }) => {
  // Configurable variables
  const baseUrl = 'https://dev.pivotol.ai/login';
  const username = 'test';
  const password = 'Br0mpt0n!0T';

  console.log('🔐 Starting login flow...');
  await page.goto(baseUrl);
  await page.getByLabel('Username *').fill(username);
  await page.getByLabel('Password *').fill(password);
  await page.getByRole('button', { name: 'Log in' }).click();
  console.log('✅ Logged in successfully');

  console.log('📊 Navigating to Analytics section...');
  await page.getByRole('button', { name: 'Alerts' }).click();
  await page.waitForTimeout(2000);
  await page.getByRole('menuitem', { name: 'Analytics' }).click();

  console.log('📅 Selecting interval: Daily');
  await page.getByLabel('Interval').click();
  await page.getByRole('option', { name: 'Daily' }).click();

  console.log('📦 Selecting Asset: MAINPANEL_MQTT');
  await page.getByLabel('Select Asset').click();
  await page.getByRole('combobox', { name: 'Select Asset' }).fill('mqtt');
  await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

  console.log('📈 Selecting Measurement: Averagecurrent');
  await page.getByLabel('Select Measurement').click();
  await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();

  console.log('📌 Clicking on the first Analytics chart tile...');
  await page.locator('.nsewdrag').first().click();

  // Optional Assertion (e.g., check if chart modal/dialog appears)
  // await expect(page.getByRole('dialog')).toBeVisible(); // update selector if needed

  // Optional Screenshot for confirmation/debug
  await page.screenshot({ path: 'analytics-positive-flow.png' });

  console.log('✅ Positive test completed successfully');
  await page.close();
});

import CancelIcon from '@mui/icons-material/Cancel';
import LaunchIcon from '@mui/icons-material/Launch';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Snackbar,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { dashboardTemplateApi, useGetDashboardTemplatesQuery } from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentAssetType,
  getCurrentDashboardId,
  isDashboardDirty,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { AssetTypeOption } from '~/types/asset';
import { DashboardState, TimeRangeOptions } from '~/types/dashboard';
import { ImageWidget } from '~/types/widgets';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import { assetsPathMapper, getPreviousDate } from '~/utils/utils';
import CustomDialog from '../common/CustomDialog';
type ImageWidgetLinkProps = {
  settings: ImageWidget;
  setSettings: React.Dispatch<React.SetStateAction<ImageWidget>>;
};
const ImageWidgetLink = ({ settings, setSettings }: ImageWidgetLinkProps) => {
  const router = useRouter();
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const { hasDashboardPermission } = useRolePermission();
  const dashboardId = useSelector(getCurrentDashboardId);

  const [openSnackBar, setOpenSnackBar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [assetToAssetType, seAssetToAssetType] = useState<number | null>(null);
  const [confirm, setConfirm] = useState<boolean>(false);

  const activeCustomer = useSelector(getActiveCustomer);
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );
  const assetsWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);
  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
    },
  );

  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery(undefined, {
    skip: settings.mode === 'dashboard' || !assetTypeTemplate || assetTypeTemplate <= 0,
  });
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  useEffect(() => {
    if (settings.imgDashboard?.dashboardOrTemplate === undefined) {
      setSettings({
        ...settings,
        imgDashboard: {
          ...settings.imgDashboard,
          dashboardOrTemplate: 'template',
        },
      });
    }
  }, [settings.imgDashboard?.dashboardOrTemplate]);
  useEffect(() => {
    if (
      assetData &&
      settings.mode === 'dashboard' &&
      settings.imgDashboard.dashboardOrTemplate === 'template' &&
      settings.imgDashboard.assetOrAssetType !== null
    ) {
      const assetToType = assetData?.find(
        (asset) => asset.id === settings.imgDashboard.assetOrAssetType,
      )?.assetTypeId;
      seAssetToAssetType(assetToType ?? null);
    }
  }, [
    settings.imgDashboard.assetOrAssetType,
    settings.mode,
    assetData,
    settings.imgDashboard.dashboardOrTemplate,
  ]);

  const { data: dashboardTemplates, isLoading: isLoadingDashboardTemplates } =
    useGetDashboardTemplatesQuery(
      {
        assetTypeId:
          settings.mode === 'dashboard'
            ? assetToAssetType ?? 0
            : settings.imgDashboard.assetOrAssetType ?? 0,
      },
      {
        skip:
          settings.mode === 'dashboard'
            ? assetToAssetType === null || assetToAssetType === 0
            : !assetTypeTemplate ||
              assetTypeTemplate <= 0 ||
              settings.imgDashboard.assetOrAssetType === null,
      },
    );
  const handleDashboardChange = () => {
    if (dashboardId === -2) {
      return false; // Do not prompt for unsaved changes if in dashboard template instance mode
    }
    if (!hasDashboardPermission('dashboard.update', Role.POWER_USER)) {
      return false;
    }
    if (
      widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
      deleteWidgets.length > 0 ||
      widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
        .length > 0 ||
      isDashboardStateDirty
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  const { data: assetMeasurements } = useGetAllMeasurementsQuery(
    {
      assetId: settings.imgDashboard.assetOrAssetType ?? 0,
      customerId: activeCustomer?.id ?? 0,
    },
    {
      skip:
        settings.mode === 'template' ||
        settings.imgDashboard.assetOrAssetType === null ||
        settings.imgDashboard.assetOrAssetType <= 0 ||
        router.pathname === '/measurement-browser',
    },
  );

  const openDashboard = async () => {
    if (handleDashboardChange()) return;
    const dashboard = settings.imgDashboard.dashboard;
    const mode = settings.mode;
    const dashboardOrTemplate = settings.imgDashboard.dashboardOrTemplate;
    if (mode === 'dashboard' && dashboard !== null) {
      // if (settings.openDashboardInNewTab) {
      //   window.open(
      //     `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id}`,
      //     '_blank',
      //   );
      //   return;
      // }
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        setOpenSnackBar({
          open: true,
          message: 'Fetching dashboard template data...',
          severity: 'info',
        });
        const {
          isError,
          error,
          data: templateData,
        } = await dispatch(
          dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(dashboard.id),
        );
        setOpenSnackBar({
          open: false,
          message: 'Dashboard template data fetched successfully!',
          severity: 'success',
        });
        if (isError && error) {
          const err = error as CustomError;
          setOpenSnackBar({
            open: true,
            message: err.data.exception ?? 'Error fetching dashboard template. Please try again.',
            severity: 'error',
          });
        }
        if (templateData && templateData?.data && assetMeasurements) {
          const templateDetailsData = JSON.parse(templateData.data) as {
            widget: DashboardState['widget'];
            topPanel: DashboardState['template']['topPanel'];
            chart: DashboardState['template']['chart'];
            desktopMobile: DashboardState['desktopMobile'];
            responsiveLayouts: DashboardState['responsiveLayouts'];
          };
          const metricToMeasurementMap: Record<string, string[]> = {};
          assetMeasurements?.forEach((measurement) => {
            if (measurement.metric_id !== null) {
              const metricIdStr = measurement.metric_id.toString();
              const measurementIdStr = measurement.id.toString();

              if (!metricToMeasurementMap[metricIdStr]) {
                metricToMeasurementMap[metricIdStr] = [];
              }

              metricToMeasurementMap[metricIdStr].push(measurementIdStr);
            }
          });
          templateDetailsData.responsiveLayouts?.desktop?.widgets.map((widget) => {
            if (widget.type === 'chart') {
              widget.settings.settings.dashboardOrTemplate =
                widget.settings.settings.dashboardOrTemplate ?? 'template';
              widget.settings.settings.assetOrAssetType =
                settings.imgDashboard.assetOrAssetType ?? null;
              widget.settings.settings.mode = 'dashboard';
              if (
                'assetMeasure' in widget.settings.settings &&
                widget.settings.settings.assetMeasure
              ) {
                if (Array.isArray(widget.settings.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings.settings) {
                    widget.settings.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.settings.selectedTitles = [];
                  }
                  widget.settings.settings.assetMeasure.push({
                    assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings.settings &&
                    metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(
                      ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                    );
                    widget.settings.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.settings.assetMeasure.assetId =
                    settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  widget.settings.settings.assetMeasure.measureId = measureIds;
                }
              }
              return widget;
            }

            widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
            widget.settings.assetOrAssetType = settings.imgDashboard.assetOrAssetType ?? null;
            widget.settings.mode = 'dashboard';
            if (widget.type === 'map') {
              widget.settings.markers = widget.settings.markers.map((marker) => {
                if (marker.selectedTitles.length > 0) {
                  const measureIds: string[] = [];
                  marker.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  measureIds.forEach((measureId) => {
                    marker.assetMeasures.push({
                      assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                      measureId: measureId,
                    });
                  });
                  let labelUnits = {};
                  measureIds.forEach((measureId) => {
                    const measure = assetMeasurements?.find(
                      (measure) => measure.id === Number(measureId),
                    );

                    labelUnits = {
                      ...labelUnits,
                      [measureId]: {
                        label: measure?.tag ?? '',
                        unit: '',
                        value: '',
                      },
                    };
                  });
                  marker.labelAndUnits = labelUnits;
                }
                marker.selectedTitles = [];
                return marker;
              });
            } else {
              if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
                if (Array.isArray(widget.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings) {
                    widget.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.selectedTitles = [];
                  }
                  if (widget.type === 'image') {
                    // const selectedDbMeasureIdToName: {
                    //   [key: string]: string;
                    // } = {};
                    // measureIds.forEach((measureId) => {
                    //   const measureName = assetMeasurements?.find(
                    //     (measure) => measure.id === Number(measureId),
                    //   )?.tag;
                    //   selectedDbMeasureIdToName[measureId] = measureName ?? '';
                    // });
                    // widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                    // const measureIdToImageTextDetails: Record<string, ImageTextDetails> = {};
                    // measureIds.forEach((measureId) => {
                    //   const measureName = assetMeasurements?.find(
                    //     (measure) => measure.id === Number(measureId),
                    //   );
                    //   const existingTextDetails =
                    //     widget.settings.measureIdToImageTextDetails[measureName?.metric_id ?? ''];
                    //   measureIdToImageTextDetails[measureId] = {
                    //     label: measureName?.tag ?? '',
                    //     unit: existingTextDetails?.unit ?? '',
                    //     id: measureId,
                    //     positionX: existingTextDetails?.positionX ?? 100,
                    //     positionY: existingTextDetails?.positionY ?? 100,
                    //     value: existingTextDetails?.value ?? '',
                    //     dashboard: existingTextDetails?.dashboard ?? null,
                    //     openDashboardInNewTab: existingTextDetails?.openDashboardInNewTab ?? false,
                    //     dashboardOrTemplate:
                    //       existingTextDetails?.dashboardOrTemplate ?? 'dashboard',
                    //     assetOrAssetType: existingTextDetails?.assetOrAssetType ?? null,
                    //   };
                    // });
                    // widget.settings.measureIdToImageTextDetails = measureIdToImageTextDetails;
                    widget.settings.imgDashboard = {
                      ...widget.settings.imgDashboard,
                      assetOrAssetType: settings.imgDashboard.assetOrAssetType ?? null,
                      dashboard: settings.imgDashboard.dashboard,
                      dashboardOrTemplate: settings.imgDashboard.dashboardOrTemplate ?? 'dashboard',
                    };
                  }
                  widget.settings.assetMeasure.push({
                    assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings &&
                    metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                    widget.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.assetMeasure.assetId =
                    settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  widget.settings.assetMeasure.measureId = measureIds;
                }
              }
            }
            if (widget.type === 'dashboard-widget') {
              const assetPath = assetsWithPath.find(
                (assetType) => assetType.value === settings.imgDashboard.assetOrAssetType,
              );
              widget.settings.assetOption = {
                id: settings.imgDashboard.assetOrAssetType ?? 0,
                label: assetPath?.label ?? '',
              };
            }
            if (widget.type === 'Diagram') {
              widget.settings.mode = 'dashboard';
              Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
                const variables = widget.settings.elementIdVariabels[elementId];

                variables.forEach((variable) => {
                  const measurementKey = variable.measurementId;
                  variable.aggBy = variable.aggBy ?? 1;
                  // Only replace if there's a mapping available
                  if (metricToMeasurementMap[measurementKey]) {
                    const mappedIds = metricToMeasurementMap[measurementKey];

                    // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                    variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                    variable.measurementId = mappedIds[0];

                    // Optional: clear label or other fields if needed
                    // variable.label = '';
                  }
                });
              });
              widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
                const measurementKey = variable.measurementId;
                // Only replace if there's a mapping available
                if (metricToMeasurementMap[measurementKey]) {
                  const mappedIds = metricToMeasurementMap[measurementKey];

                  // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                  variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  variable.measurementId = mappedIds[0];

                  // Optional: clear label or other fields if needed
                  // variable.label = '';
                }
                variable.aggBy = variable.aggBy ?? 1;
                return variable;
              });
            }
            return widget;
          });
          templateDetailsData.responsiveLayouts?.mobile?.widgets.map((widget) => {
            if (widget.type === 'chart') {
              widget.settings.settings.dashboardOrTemplate =
                widget.settings.settings.dashboardOrTemplate ?? 'template';
              widget.settings.settings.assetOrAssetType =
                settings.imgDashboard.assetOrAssetType ?? null;
              widget.settings.settings.mode = 'dashboard';
              if (
                'assetMeasure' in widget.settings.settings &&
                widget.settings.settings.assetMeasure
              ) {
                if (Array.isArray(widget.settings.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings.settings) {
                    widget.settings.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.settings.selectedTitles = [];
                  }
                  widget.settings.settings.assetMeasure.push({
                    assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings.settings &&
                    metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(
                      ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                    );
                    widget.settings.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.settings.assetMeasure.assetId =
                    settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  widget.settings.settings.assetMeasure.measureId = measureIds;
                }
              }
              return widget;
            }

            widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
            widget.settings.assetOrAssetType = settings.imgDashboard.assetOrAssetType ?? null;
            widget.settings.mode = 'dashboard';
            if (widget.type === 'map') {
              widget.settings.markers = widget.settings.markers.map((marker) => {
                if (marker.selectedTitles.length > 0) {
                  const measureIds: string[] = [];
                  marker.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  measureIds.forEach((measureId) => {
                    marker.assetMeasures.push({
                      assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                      measureId: measureId,
                    });
                  });
                  let labelUnits = {};
                  measureIds.forEach((measureId) => {
                    const measure = assetMeasurements?.find(
                      (measure) => measure.id === Number(measureId),
                    );

                    labelUnits = {
                      ...labelUnits,
                      [measureId]: {
                        label: measure?.tag ?? '',
                        unit: '',
                        value: '',
                      },
                    };
                  });
                  marker.labelAndUnits = labelUnits;
                }
                marker.selectedTitles = [];
                return marker;
              });
            } else {
              if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
                if (Array.isArray(widget.settings.assetMeasure)) {
                  const measureIds: string[] = [];
                  if ('selectedTitles' in widget.settings) {
                    widget.settings.selectedTitles.forEach((title) => {
                      if (metricToMeasurementMap[title]) {
                        measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                      }
                    });
                    widget.settings.selectedTitles = [];
                  }
                  if (widget.type === 'image') {
                    // const selectedDbMeasureIdToName: {
                    //   [key: string]: string;
                    // } = {};
                    // measureIds.forEach((measureId) => {
                    //   const measureName = assetMeasurements?.find(
                    //     (measure) => measure.id === Number(measureId),
                    //   )?.tag;
                    //   selectedDbMeasureIdToName[measureId] = measureName ?? '';
                    // });
                    // widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                    // const measureIdToImageTextDetails: Record<string, ImageTextDetails> = {};
                    // measureIds.forEach((measureId) => {
                    //   const measureName = assetMeasurements?.find(
                    //     (measure) => measure.id === Number(measureId),
                    //   );
                    //   const existingTextDetails =
                    //     widget.settings.measureIdToImageTextDetails[measureName?.metric_id ?? ''];
                    //   measureIdToImageTextDetails[measureId] = {
                    //     label: measureName?.tag ?? '',
                    //     unit: existingTextDetails?.unit ?? '',
                    //     id: measureId,
                    //     positionX: existingTextDetails?.positionX ?? 100,
                    //     positionY: existingTextDetails?.positionY ?? 100,
                    //     value: existingTextDetails?.value ?? '',
                    //     dashboard: existingTextDetails?.dashboard ?? null,
                    //     openDashboardInNewTab: existingTextDetails?.openDashboardInNewTab ?? false,
                    //     dashboardOrTemplate:
                    //       existingTextDetails?.dashboardOrTemplate ?? 'dashboard',
                    //     assetOrAssetType: existingTextDetails?.assetOrAssetType ?? null,
                    //   };
                    // });
                    // widget.settings.measureIdToImageTextDetails = measureIdToImageTextDetails;
                    widget.settings.imgDashboard = {
                      ...widget.settings.imgDashboard,
                      assetOrAssetType: settings.imgDashboard.assetOrAssetType ?? null,
                      dashboard: settings.imgDashboard.dashboard,
                      dashboardOrTemplate: settings.imgDashboard.dashboardOrTemplate ?? 'dashboard',
                    };
                  }
                  widget.settings.assetMeasure.push({
                    assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                    measureId: measureIds,
                  });
                } else {
                  const measureIds: string[] = [];
                  if (
                    'selectedDbMeasureId' in widget.settings &&
                    metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                  ) {
                    measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                    widget.settings.selectedDbMeasureId = '';
                  }
                  widget.settings.assetMeasure.assetId =
                    settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  widget.settings.assetMeasure.measureId = measureIds;
                }
              }
            }
            if (widget.type === 'dashboard-widget') {
              const assetPath = assetsWithPath.find(
                (assetType) => assetType.value === settings.imgDashboard.assetOrAssetType,
              );
              widget.settings.assetOption = {
                id: settings.imgDashboard.assetOrAssetType ?? 0,
                label: assetPath?.label ?? '',
              };
            }
            if (widget.type === 'Diagram') {
              widget.settings.mode = 'dashboard';
              Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
                const variables = widget.settings.elementIdVariabels[elementId];

                variables.forEach((variable) => {
                  const measurementKey = variable.measurementId;
                  variable.aggBy = variable.aggBy ?? 1;
                  // Only replace if there's a mapping available
                  if (metricToMeasurementMap[measurementKey]) {
                    const mappedIds = metricToMeasurementMap[measurementKey];

                    // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                    variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                    variable.measurementId = mappedIds[0];

                    // Optional: clear label or other fields if needed
                    // variable.label = '';
                  }
                });
              });
              widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
                const measurementKey = variable.measurementId;
                // Only replace if there's a mapping available
                if (metricToMeasurementMap[measurementKey]) {
                  const mappedIds = metricToMeasurementMap[measurementKey];

                  // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                  variable.assetId = settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                  variable.measurementId = mappedIds[0];

                  // Optional: clear label or other fields if needed
                  // variable.label = '';
                }
                variable.aggBy = variable.aggBy ?? 1;
                return variable;
              });
            }
            return widget;
          });

          dispatch(
            dashboardSlice.actions.setDashboardCrumb({
              dashboardId: -2,
              title: dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
              templateId: templateData?.id,
              assetType: templateData?.asset_template?.assetType?.id ?? undefined,
              assetId: settings.imgDashboard.assetOrAssetType ?? undefined,
            }),
          );
          router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
          dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
          dispatch(
            dashboardSlice.actions.setCurrentDashboardTitle(
              dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
            ),
          );
          const lastWidget = templateDetailsData.responsiveLayouts?.desktop?.widgets
            ?.sort((a, b) => Number(a.id) - Number(b.id))
            ?.at(-1);
          dispatch(
            dashboardSlice.actions.setWidget({
              widgets: templateDetailsData.responsiveLayouts?.desktop?.widgets ?? [],
              deleteWidgets: [],
              widgetLayout: templateDetailsData.responsiveLayouts?.desktop?.widgetLayout ?? [],
              lastWidgetId: Number(lastWidget?.id) ?? 0,
            }),
          );
          dispatch(
            dashboardSlice.actions.setWidgetsLayout(templateDetailsData.widget.widgetLayout),
          );
          dispatch(
            dashboardSlice.actions.setDesktopMobileMode(templateDetailsData.desktopMobile ?? 0),
          );
          dispatch(
            dashboardSlice.actions.setResponsiveLayouts({
              desktop: {
                widgetLayout: templateDetailsData.responsiveLayouts?.desktop?.widgetLayout ?? [],
                widgets: templateDetailsData.responsiveLayouts?.desktop?.widgets ?? [],
              },
              mobile: {
                widgetLayout: templateDetailsData.responsiveLayouts?.mobile?.widgetLayout ?? [],
                widgets: templateDetailsData.responsiveLayouts?.mobile?.widgets ?? [],
              },
            }),
          );
          dispatch(
            dashboardSlice.actions.setSamplePeriod(templateDetailsData.topPanel.samplePeriod),
          );
          dispatch(
            dashboardSlice.actions.setRefreshTimeInterval(
              templateDetailsData.topPanel.refreshInterval,
            ),
          );
          dispatch(
            dashboardSlice.actions.setTimeRangeType(templateDetailsData.topPanel.timeRangeType),
          );
          const minutes: number =
            TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6].serverValue;
          const start = getPreviousDate(minutes);
          if (templateDetailsData.topPanel.timeRangeType !== 0) {
            dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
            dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
          } else {
            dispatch(
              dashboardSlice.actions.setChartStartDate(
                new Date(templateDetailsData.chart.startDate),
              ),
            );
            dispatch(
              dashboardSlice.actions.setChartEndDate(new Date(templateDetailsData.chart.endDate)),
            );
          }
        }
      }
    }
    if (mode === 'template' && dashboard !== null) {
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        dispatch(dashboardSlice.actions.setTemplateId(dashboard.id));
        dispatch(dashboardSlice.actions.setTemplateName(dashboard.title));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: [],
            deleteWidgets: [],
            widgetLayout: [],
            lastWidgetId: 0,
          }),
        );
      }
    }
  };
  return (
    <>
      <Snackbar
        open={openSnackBar.open}
        onClose={() => {
          setOpenSnackBar({ open: false, message: '', severity: 'info' });
        }}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={openSnackBar.severity}>{openSnackBar.message}</Alert>
      </Snackbar>
      <FormControl component="fieldset">
        <RadioGroup
          row
          value={settings.imgDashboard.dashboardOrTemplate ?? 'template'}
          onChange={(event: React.ChangeEvent<HTMLInputElement>, value: string) => {
            setSettings((prevState) => ({
              ...prevState,
              imgDashboard: {
                ...prevState.imgDashboard,
                dashboardOrTemplate: value as 'dashboard' | 'template',
                dashboard: null,
              },
            }));
          }}
          name="dashboardOrTemplate"
        >
          <FormControlLabel value="template" control={<Radio />} label="Template" />
          <FormControlLabel value="dashboard" control={<Radio />} label="Dashboard" />
        </RadioGroup>
      </FormControl>
      {settings.mode === 'template' && settings.imgDashboard.dashboardOrTemplate === 'template' ? (
        <Box sx={{ display: 'flex' }}>
          <Autocomplete
            fullWidth
            disablePortal
            loading={isAssetReloading}
            id="combo-box-demo"
            options={assetTypesWithPath.map((item) => {
              return {
                id: item.value.toString(),
                label: item.label,
              };
            })}
            value={
              assetTypesWithPath
                .map((item) => {
                  return {
                    id: item.value.toString(),
                    label: item.label,
                  };
                })
                .find((item) => item.id === settings!.imgDashboard!.assetOrAssetType?.toString()) ??
              null
            }
            onChange={(event, value) => {
              setSettings({
                ...settings,
                imgDashboard: {
                  ...settings.imgDashboard,
                  assetOrAssetType: Number(value?.id) ?? null,
                  dashboard: null,
                },
              });
            }}
            renderInput={(params) => <TextField {...params} label="Asset Type" />}
          />
        </Box>
      ) : null}
      {settings.mode === 'dashboard' && settings.imgDashboard.dashboardOrTemplate === 'template' ? (
        <Box sx={{ display: 'flex' }}>
          <Autocomplete
            fullWidth
            id={`asset-autocomplete`}
            loading={isAssetReloading}
            options={
              !isAssetReloading
                ? assetsWithPath.map((asset) => ({
                    label: asset.label,
                    id: asset.id,
                  }))
                : []
            }
            getOptionLabel={(option) => option?.label ?? ''}
            onChange={(event, value) => {
              setSettings({
                ...settings,
                imgDashboard: {
                  ...settings.imgDashboard,
                  assetOrAssetType: value?.id ?? null,
                  dashboard: null,
                },
              });
            }}
            value={assetsWithPath?.find(
              (asset) => asset.id === settings!.imgDashboard?.assetOrAssetType,
            )}
            renderInput={(params) => (
              <TextField {...params} label="Select Asset" variant="outlined" />
            )}
          />
        </Box>
      ) : null}
      <Box sx={{ display: 'flex', gap: 1, mt: 3 }}>
        <Autocomplete
          loading={isLoadingDashboards || isLoadingDashboardTemplates}
          id="dashboards-combo-box"
          options={
            settings.imgDashboard.dashboardOrTemplate === 'dashboard'
              ? dashboardList?.items?.map((dashboard) => ({
                  id: dashboard.id,
                  title: dashboard.title,
                })) ?? []
              : dashboardTemplates?.items?.map((dashboardTemp) => ({
                  id: dashboardTemp.id,
                  title: dashboardTemp.title,
                })) ?? []
          }
          getOptionLabel={(option) => option.title}
          onChange={(e: React.SyntheticEvent, value: { id: number; title: string } | null) => {
            setSettings((prevState) => ({
              ...prevState,
              imgDashboard: {
                ...prevState.imgDashboard,
                dashboard: value,
              },
            }));
          }}
          sx={{ width: '100%' }}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          value={settings.imgDashboard.dashboard ?? null}
          loadingText={
            settings.imgDashboard.dashboardOrTemplate === 'dashboard'
              ? 'Loading Dashboards...'
              : 'Loading Dashboard Templates...'
          }
          renderInput={(params) => (
            <TextField
              {...params}
              label={
                settings.imgDashboard.dashboardOrTemplate === 'template'
                  ? 'Link Dashboard Template'
                  : 'Link Dashboard'
              }
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {isLoadingDashboards || isLoadingDashboardTemplates ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
        {settings.imgDashboard.dashboard !== null && settings.imgDashboard.dashboard?.id && (
          <IconButton
            disableRipple
            disableTouchRipple
            id={'title-widget-link-icon'}
            edge="start"
            color="inherit"
            sx={{
              zIndex: 10,
              mr: 0.5,
            }}
            onClick={openDashboard}
          >
            <Tooltip
              title={
                // <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                //   Open Dashboard - {settings.imgDashboard.dashboard.title}
                // </Typography>
                <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                  {settings.imgDashboard.dashboardOrTemplate === 'dashboard' ? (
                    <>Open Dashboard - {settings?.imgDashboard.dashboard.title}</>
                  ) : (
                    <>Open Dashboard Template- {settings?.imgDashboard.dashboard.title}</>
                  )}

                  {settings.mode}
                </Typography>
              }
            >
              <LaunchIcon />
            </Tooltip>
          </IconButton>
        )}
      </Box>
      {settings.imgDashboard.dashboard !== null && settings.imgDashboard.dashboard?.id && (
        <FormControl fullWidth>
          <FormControlLabel
            control={
              <Checkbox
                sx={{ mt: 1, mb: 1 }}
                color="primary"
                checked={settings.imgDashboard.openDashboardInNewTab}
                onChange={(event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
                  if (checked) {
                    setSettings((prevState) => ({
                      ...prevState,
                      imgDashboard: {
                        ...prevState.imgDashboard,
                        openDashboardInNewTab: true,
                      },
                    }));
                  } else {
                    setSettings((prevState) => ({
                      ...prevState,
                      imgDashboard: {
                        ...prevState.imgDashboard,
                        openDashboardInNewTab: false,
                      },
                    }));
                  }
                }}
              />
            }
            label="Open in New Tab"
          />
        </FormControl>
      )}

      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                setConfirm(false);
                const dashboard = settings.imgDashboard.dashboard;
                const mode = settings.mode;
                const dashboardOrTemplate = settings.imgDashboard.dashboardOrTemplate;
                if (mode === 'dashboard' && dashboard !== null) {
                  // if (settings.openDashboardInNewTab) {
                  //   window.open(
                  //     `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id}`,
                  //     '_blank',
                  //   );
                  //   return;
                  // }
                  if (dashboardOrTemplate === 'dashboard') {
                    dispatch(
                      dashboardSlice.actions.setDashboardCrumb({
                        dashboardId: dashboard.id,
                        title: dashboard.title,
                      }),
                    );
                    dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
                    dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
                    router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
                  }
                  if (dashboardOrTemplate === 'template') {
                    setOpenSnackBar({
                      open: true,
                      message: 'Fetching dashboard template data...',
                      severity: 'info',
                    });
                    const {
                      isError,
                      error,
                      data: templateData,
                    } = await dispatch(
                      dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(
                        dashboard.id,
                      ),
                    );
                    setOpenSnackBar({
                      open: false,
                      message: 'Dashboard template data fetched successfully!',
                      severity: 'success',
                    });
                    if (isError && error) {
                      const err = error as CustomError;
                      setOpenSnackBar({
                        open: true,
                        message:
                          err.data.exception ??
                          'Error fetching dashboard template. Please try again.',
                        severity: 'error',
                      });
                    }
                    if (templateData && templateData?.data && assetMeasurements) {
                      const templateDetailsData = JSON.parse(templateData.data) as {
                        widget: DashboardState['widget'];
                        topPanel: DashboardState['template']['topPanel'];
                        chart: DashboardState['template']['chart'];
                      };
                      const metricToMeasurementMap: Record<string, string[]> = {};
                      assetMeasurements?.forEach((measurement) => {
                        if (measurement.metric_id !== null) {
                          const metricIdStr = measurement.metric_id.toString();
                          const measurementIdStr = measurement.id.toString();

                          if (!metricToMeasurementMap[metricIdStr]) {
                            metricToMeasurementMap[metricIdStr] = [];
                          }

                          metricToMeasurementMap[metricIdStr].push(measurementIdStr);
                        }
                      });

                      templateDetailsData.widget.widgets.map((widget) => {
                        if (widget.type === 'chart') {
                          widget.settings.settings.dashboardOrTemplate =
                            widget.settings.settings.dashboardOrTemplate ?? 'template';
                          widget.settings.settings.assetOrAssetType =
                            settings.imgDashboard.assetOrAssetType ?? null;
                          widget.settings.settings.mode = 'dashboard';
                          if (
                            'assetMeasure' in widget.settings.settings &&
                            widget.settings.settings.assetMeasure
                          ) {
                            if (Array.isArray(widget.settings.settings.assetMeasure)) {
                              const measureIds: string[] = [];
                              if ('selectedTitles' in widget.settings.settings) {
                                widget.settings.settings.selectedTitles.forEach((title) => {
                                  if (metricToMeasurementMap[title]) {
                                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                                  }
                                });
                                widget.settings.settings.selectedTitles = [];
                              }
                              widget.settings.settings.assetMeasure.push({
                                assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                                measureId: measureIds,
                              });
                            } else {
                              const measureIds: string[] = [];
                              if (
                                'selectedDbMeasureId' in widget.settings.settings &&
                                metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                              ) {
                                measureIds.push(
                                  ...metricToMeasurementMap[
                                    widget.settings.settings.selectedDbMeasureId
                                  ],
                                );
                                widget.settings.settings.selectedDbMeasureId = '';
                              }
                              widget.settings.settings.assetMeasure.assetId =
                                settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                              widget.settings.settings.assetMeasure.measureId = measureIds;
                            }
                          }
                          return widget;
                        }

                        widget.settings.dashboardOrTemplate =
                          widget.settings.dashboardOrTemplate ?? 'template';
                        widget.settings.assetOrAssetType =
                          settings.imgDashboard.assetOrAssetType ?? null;
                        widget.settings.mode = 'dashboard';
                        if (widget.type === 'map') {
                          widget.settings.markers = widget.settings.markers.map((marker) => {
                            if (marker.selectedTitles.length > 0) {
                              const measureIds: string[] = [];
                              marker.selectedTitles.forEach((title) => {
                                if (metricToMeasurementMap[title]) {
                                  measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                                }
                              });
                              measureIds.forEach((measureId) => {
                                marker.assetMeasures.push({
                                  assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                                  measureId: measureId,
                                });
                              });
                              let labelUnits = {};
                              measureIds.forEach((measureId) => {
                                const measure = assetMeasurements?.find(
                                  (measure) => measure.id === Number(measureId),
                                );

                                labelUnits = {
                                  ...labelUnits,
                                  [measureId]: {
                                    label: measure?.tag ?? '',
                                    unit: '',
                                    value: '',
                                  },
                                };
                              });
                              marker.labelAndUnits = labelUnits;
                            }
                            marker.selectedTitles = [];
                            return marker;
                          });
                        } else {
                          if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
                            if (Array.isArray(widget.settings.assetMeasure)) {
                              const measureIds: string[] = [];
                              if ('selectedTitles' in widget.settings) {
                                widget.settings.selectedTitles.forEach((title) => {
                                  if (metricToMeasurementMap[title]) {
                                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                                  }
                                });
                                widget.settings.selectedTitles = [];
                              }
                              if (widget.type === 'image') {
                                // const selectedDbMeasureIdToName: {
                                //   [key: string]: string;
                                // } = {};
                                // measureIds.forEach((measureId) => {
                                //   const measureName = assetMeasurements?.find(
                                //     (measure) => measure.id === Number(measureId),
                                //   )?.tag;
                                //   selectedDbMeasureIdToName[measureId] = measureName ?? '';
                                // });
                                // widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                                // const measureIdToImageTextDetails: Record<
                                //   string,
                                //   ImageTextDetails
                                // > = {};
                                // measureIds.forEach((measureId) => {
                                //   const measureName = assetMeasurements?.find(
                                //     (measure) => measure.id === Number(measureId),
                                //   );
                                //   const existingTextDetails =
                                //     widget.settings.measureIdToImageTextDetails[
                                //       measureName?.metric_id ?? ''
                                //     ];
                                //   measureIdToImageTextDetails[measureId] = {
                                //     label: measureName?.tag ?? '',
                                //     unit: existingTextDetails?.unit ?? '',
                                //     id: measureId,
                                //     positionX: existingTextDetails?.positionX ?? 100,
                                //     positionY: existingTextDetails?.positionY ?? 100,
                                //     value: existingTextDetails?.value ?? '',
                                //     dashboard: existingTextDetails?.dashboard ?? null,
                                //     openDashboardInNewTab:
                                //       existingTextDetails?.openDashboardInNewTab ?? false,
                                //     dashboardOrTemplate:
                                //       existingTextDetails?.dashboardOrTemplate ?? 'dashboard',
                                //     assetOrAssetType: existingTextDetails?.assetOrAssetType ?? null,
                                //   };
                                // });
                                // widget.settings.measureIdToImageTextDetails =
                                //   measureIdToImageTextDetails;
                                widget.settings.imgDashboard = {
                                  ...widget.settings.imgDashboard,
                                  assetOrAssetType: settings.imgDashboard.assetOrAssetType ?? null,
                                  dashboard: settings.imgDashboard.dashboard,
                                  dashboardOrTemplate:
                                    settings.imgDashboard.dashboardOrTemplate ?? 'dashboard',
                                };
                              }
                              widget.settings.assetMeasure.push({
                                assetId: settings.imgDashboard.assetOrAssetType?.toString() ?? '',
                                measureId: measureIds,
                              });
                            } else {
                              const measureIds: string[] = [];
                              if (
                                'selectedDbMeasureId' in widget.settings &&
                                metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                              ) {
                                measureIds.push(
                                  ...metricToMeasurementMap[widget.settings.selectedDbMeasureId],
                                );
                                widget.settings.selectedDbMeasureId = '';
                              }
                              widget.settings.assetMeasure.assetId =
                                settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                              widget.settings.assetMeasure.measureId = measureIds;
                            }
                          }
                        }
                        if (widget.type === 'dashboard-widget') {
                          const assetPath = assetsWithPath.find(
                            (assetType) =>
                              assetType.value === settings.imgDashboard.assetOrAssetType,
                          );
                          widget.settings.assetOption = {
                            id: settings.imgDashboard.assetOrAssetType ?? 0,
                            label: assetPath?.label ?? '',
                          };
                        }
                        if (widget.type === 'Diagram') {
                          widget.settings.mode = 'dashboard';
                          Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
                            const variables = widget.settings.elementIdVariabels[elementId];

                            variables.forEach((variable) => {
                              const measurementKey = variable.measurementId;
                              variable.aggBy = variable.aggBy ?? 1;
                              // Only replace if there's a mapping available
                              if (metricToMeasurementMap[measurementKey]) {
                                const mappedIds = metricToMeasurementMap[measurementKey];

                                // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                                variable.assetId =
                                  settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                                variable.measurementId = mappedIds[0];

                                // Optional: clear label or other fields if needed
                                // variable.label = '';
                              }
                            });
                          });
                          widget.settings.elementVariable = widget.settings.elementVariable.map(
                            (variable) => {
                              const measurementKey = variable.measurementId;
                              // Only replace if there's a mapping available
                              if (metricToMeasurementMap[measurementKey]) {
                                const mappedIds = metricToMeasurementMap[measurementKey];

                                // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                                variable.assetId =
                                  settings.imgDashboard.assetOrAssetType?.toString() ?? '';
                                variable.measurementId = mappedIds[0];

                                // Optional: clear label or other fields if needed
                                // variable.label = '';
                              }
                              variable.aggBy = variable.aggBy ?? 1;
                              return variable;
                            },
                          );
                        }
                        return widget;
                      });
                      dispatch(
                        dashboardSlice.actions.setDashboardCrumb({
                          dashboardId: -2,
                          title:
                            dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
                          templateId: templateData?.id,
                          assetType: templateData?.asset_template?.assetType?.id ?? undefined,
                          assetId: settings.imgDashboard.assetOrAssetType ?? undefined,
                        }),
                      );
                      router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
                      dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
                      dispatch(
                        dashboardSlice.actions.setCurrentDashboardTitle(
                          dashboard?.title + '-' + templateData?.asset_template?.assetType?.name,
                        ),
                      );
                      dispatch(
                        dashboardSlice.actions.setWidget({
                          widgets: templateDetailsData.widget.widgets,
                          deleteWidgets: [],
                          widgetLayout: templateDetailsData.widget.widgetLayout,
                          lastWidgetId: templateDetailsData.widget.lastWidgetId,
                        }),
                      );
                      dispatch(
                        dashboardSlice.actions.setWidgetsLayout(
                          templateDetailsData.widget.widgetLayout,
                        ),
                      );
                      dispatch(
                        dashboardSlice.actions.setSamplePeriod(
                          templateDetailsData.topPanel.samplePeriod,
                        ),
                      );
                      dispatch(
                        dashboardSlice.actions.setRefreshTimeInterval(
                          templateDetailsData.topPanel.refreshInterval,
                        ),
                      );
                      dispatch(
                        dashboardSlice.actions.setTimeRangeType(
                          templateDetailsData.topPanel.timeRangeType,
                        ),
                      );
                      const minutes: number =
                        TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6]
                          .serverValue;
                      const start = getPreviousDate(minutes);
                      if (templateDetailsData.topPanel.timeRangeType !== 0) {
                        dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
                        dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
                      } else {
                        dispatch(
                          dashboardSlice.actions.setChartStartDate(
                            new Date(templateDetailsData.chart.startDate),
                          ),
                        );
                        dispatch(
                          dashboardSlice.actions.setChartEndDate(
                            new Date(templateDetailsData.chart.endDate),
                          ),
                        );
                      }
                    }
                  }
                }
                if (mode === 'template' && dashboard !== null) {
                  if (dashboardOrTemplate === 'dashboard') {
                    dispatch(
                      dashboardSlice.actions.setDashboardCrumb({
                        dashboardId: dashboard.id,
                        title: dashboard.title,
                      }),
                    );
                    dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
                    dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
                    router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
                  }
                  if (dashboardOrTemplate === 'template') {
                    dispatch(dashboardSlice.actions.setTemplateId(dashboard.id));
                    dispatch(dashboardSlice.actions.setTemplateName(dashboard.title));
                    dispatch(
                      dashboardSlice.actions.setWidget({
                        widgets: [],
                        deleteWidgets: [],
                        widgetLayout: [],
                        lastWidgetId: 0,
                      }),
                    );
                  }
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};

export default ImageWidgetLink;

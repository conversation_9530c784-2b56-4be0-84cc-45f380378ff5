import { test, expect } from '@playwright/test';
test('update user preferences', async ({ page }) => {
    await page.goto('https://dev.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.setViewportSize({ width: 1920, height: 1080 });
  await page.getByRole('button', { name: 'D', exact: true }).click();
  await page.waitForTimeout(3000);
  await page.getByText('User Preferences').click();
  await page.waitForTimeout(5000);

// Scroll manually to top to avoid overlapping elements
await page.evaluate(() => window.scrollTo(0, 0));

// Wait for 'Select Customer' dropdown to be attached and visible
const customerDropdown = page.getByLabel('Select Customer');
await customerDropdown.waitFor({ state: 'visible' });

// Force hover (sometimes helps trigger visibility in MUI)
await customerDropdown.hover();

// Click only after ensuring it's stable and not overlapped
await customerDropdown.click();

// Proceed with selecting the option
await page.getByRole('option', { name: 'Brompton Energy Inc.' }).click();

  //await page.getByLabel('Select Customer').click();
  //await page.getByRole('option', { name: 'Brompton Energy Inc.' }).click();
  await page.getByLabel('Select Date-Time Format').click();
  await page.getByRole('option', { name: 'DD-MM-YYYY hh:mm:ss a' }).click();
  //await page.locator('svg.MuiSvgIcon-root[data-testid="CheckBoxOutlineBlankIcon"]').click();

  await page.getByRole('button', { name: 'Save' }).click();
  await page.waitForTimeout(1000);
  await page.close();
});
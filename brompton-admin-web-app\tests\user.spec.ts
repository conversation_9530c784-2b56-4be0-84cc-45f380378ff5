import { test, expect } from '@playwright/test';
test('test', async ({ page }) => {
        await page.goto('https://dev.pivotol.ai/login');
        await page.getByLabel('Username *').click();
        await page.getByLabel('Username *').fill('test');
        await page.getByLabel('Password *').click();
        await page.getByLabel('Password *').fill('Br0mpt0n!0T');
        await page.getByRole('button', { name: 'Log in' }).click();
        await page.getByRole('button', { name: 'Add Dashboard' }).click();
        await page.waitForTimeout(5000);
        await page.getByRole('link', { name: 'Users' }).click();
        await page.getByRole('button', { name: 'Add User' }).click();
        await page.getByLabel('First Name *').click();
        await page.getByLabel('First Name *').fill('avatar');
        await page.getByLabel('Last Name *').click();
        await page.getByLabel('Last Name *').fill('prime');
        await page.getByLabel('Username *').click();
        await page.getByLabel('Username *').fill('avatar');
        await page.getByLabel('Password *').click();
        await page.getByLabel('Password *').fill('prime');
        await page.getByLabel('Email *').click();
        await page.getByLabel('Email *').fill('<EMAIL>');
        await page.getByLabel('Country Code *').click();
        await page.getByLabel('Country Code *').fill('+91');
        await page.getByLabel('Phone Number *').click();
        await page.getByLabel('Phone Number *').fill('9763707433');
        await page.getByLabel('Select Customer for User').click();
        await page.getByRole('option', { name: 'Brompton Energy Inc.' }).click();
        await page.getByLabel('Select Customer for Power User').click();
        await page.getByRole('option', { name: 'Carbon Energy' }).click();
        await page.getByLabel('Select Customer for Admin').click();
        await page.getByRole('option', { name: 'Pivotal Energy' }).click();
        await page.getByRole('button', { name: 'Submit' }).click();
        await page.close();
      });
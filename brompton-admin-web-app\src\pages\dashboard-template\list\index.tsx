import AddIcon from '@mui/icons-material/Add';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import {
  Alert,
  Box,
  Button,
  Chip,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Drawer,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Snackbar,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import QuickSearchToolbar from '~/components/common/DataTable/QuickSearchToolbar';
import Loader from '~/components/common/Loader';
import CustomNoRowsOverlay from '~/components/common/NoRowsOverlay';
import PageName from '~/components/common/PageName/PageName';
import useExportDashboardTemplate from '~/hooks/useExportDashboadTemplate';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import {
  useDeleteDashboardTemplateMutation,
  useGetDashboardTemplatesQuery,
} from '~/redux/api/dashboardTemplate';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetTypeOption } from '~/types/asset';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';

const List = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [localAssetTypeId, setLocalAssetTypeId] = useState('');
  const [localTemplateName, setLocalTemplateName] = useState('');
  const [localIsGlobal, setLocalIsGlobal] = useState('');
  const [localModelNumber, setLocalModelNumber] = useState('');
  const [showFilter, setShowFilter] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState({
    assetTypeId: '',
    isGlobal: '',
    modelNumber: '',
    templateName: '',
  });

  const [deleteDashboardTemplate, setDeleteDashboardTemplate] = useState<{
    open: boolean;
    dashboardTemplateId: number;
  }>({ open: false, dashboardTemplateId: 0 });

  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const { exportTemplate, loading, message, setMessage } = useExportDashboardTemplate();
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const { data, isFetching: fetchingTemplates, refetch } = useGetDashboardTemplatesQuery({});
  const { data: assetTypeListData, isSuccess: isSuccessfullBackOffieAssetTypes } =
    useGetAllBackOfficeAssetTypesQuery();
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  const [deleteDashboardTemplateDetails, { isError, isLoading, isSuccess }] =
    useDeleteDashboardTemplateMutation();
  const handleApply = () => {
    setAppliedFilters({
      assetTypeId: localAssetTypeId,
      isGlobal: localIsGlobal,
      modelNumber: localModelNumber,
      templateName: localTemplateName,
    });
    setShowFilter(false);
  };

  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('Dashboard deleted successfully');
      refetch();
    }
    if (isError) {
      showErrorAlert('Error deleting dashboard');
    }
  }, [isError, isSuccess]);

  const handleDelete = async () => {
    await deleteDashboardTemplateDetails(deleteDashboardTemplate.dashboardTemplateId);
    setDeleteDashboardTemplate({ dashboardTemplateId: 0, open: false });
  };
  const columns: GridColDef[] = [
    {
      field: 'title',
      headerName: 'Template Name',
      flex: 2,
      renderCell: (params) => (
        <Typography
          sx={{ cursor: 'pointer', color: 'primary.main', textDecoration: 'underline' }}
          onClick={() => {
            const item = params.row;
            dispatch(dashboardSlice.actions.setTemplateId(item.id));
            dispatch(dashboardSlice.actions.setTemplateName(item.title));
            dispatch(dashboardSlice.actions.setAssetTemplate(item.asset_template.id));
            dispatch(dashboardSlice.actions.setAssetType(item.asset_template.assetType.id));
            router.push('/dashboard-template');
          }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'modelNumber',
      headerName: 'Model Number',
      flex: 1,
      valueGetter: (params) => params.row.asset_template?.modelNumber || 'N/A',
    },
    {
      field: 'assetTypeName',
      headerName: 'Asset Type Name',
      flex: 2,
      valueGetter: (params) =>
        getAssetTypeLabel(params.row.asset_template?.assetType?.id?.toString() || ''),
    },
    {
      field: 'customer',
      headerName: 'Global Template',
      flex: 1,
      renderCell: (params) => {
        return <Typography>{params.row.customer === null ? 'Yes' : 'No'}</Typography>;
      },
    },
    {
      field: 'createdAt',
      headerName: 'Created At (UTC)',
      flex: 1.5,
      valueGetter: (params) => dayjs(params.row.createdat).format(dateTimeFormat),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      sortable: false,
      headerAlign: 'center',
      flex: 1,
      align: 'center',
      renderCell: (params) => (
        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}>
          <Tooltip title="Export Template">
            <Button color="primary" onClick={() => exportTemplate(params.row.id)}>
              <FileDownloadIcon />
            </Button>
          </Tooltip>
          <Tooltip title="Delete Dashboard">
            <Button
              color="error"
              onClick={() =>
                setDeleteDashboardTemplate({ open: true, dashboardTemplateId: params.row.id })
              }
            >
              <DeleteIcon />
            </Button>
          </Tooltip>
        </Box>
      ),
    },
  ];
  const getAssetTypeLabel = (id: string) => {
    const found = assetTypesWithPath.find((type) => type.value?.toString() === id);
    return found ? found.label : id;
  };

  return (
    <Container
      sx={{
        mt: 2,
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <PageName name="Dashboard Template List" />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2 }}>
            <Button
              color="primary"
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                dispatch(dashboardSlice.actions.setTemplateId(0));
                dispatch(dashboardSlice.actions.setTemplateName(''));
                dispatch(
                  dashboardSlice.actions.setWidget({
                    widgets: [],
                    deleteWidgets: [],
                    widgetLayout: [],
                    lastWidgetId: 0,
                  }),
                );
                dispatch(dashboardSlice.actions.setDesktopMobileMode(0));
                dispatch(
                  dashboardSlice.actions.setResponsiveLayouts({
                    desktop: { widgetLayout: [], widgets: [] },
                    mobile: { widgetLayout: [], widgets: [] },
                  }),
                );
                dispatch(dashboardSlice.actions.setWidget({
                  widgets: [],
                  deleteWidgets: [],
                  widgetLayout: [],
                  lastWidgetId: 0,
                }));
                dispatch(dashboardSlice.actions.setWidgetsLayout([]));
                dispatch(dashboardSlice.actions.setAssetType(0));
                dispatch(dashboardSlice.actions.setAssetTemplate(0));
                router.push('/dashboard-template');
              }}
            >
              New Dashboard Template
            </Button>
          </Box>
        </Box>
        <AlertSnackbar {...snackbarState} />

        {fetchingTemplates ? (
          <Loader />
        ) : (
          <Box sx={{ height: 'calc(100vh - 75px)', width: '100%' }}>
            <DataGrid
              sx={{
                '& .MuiDataGrid-columnHeader': {
                  background: '#F9FAFB',
                },
              }}
              autoPageSize
              rows={(data?.items || []).filter((item) => {
                const { assetTypeId, isGlobal, modelNumber, templateName } = appliedFilters;
                const matchAssetType =
                  !assetTypeId || String(item.asset_template?.assetType?.id) === assetTypeId;

                const matchGlobal =
                  !isGlobal ||
                  (isGlobal === 'yes' && item.customer === null) ||
                  (isGlobal === 'no' && item.customer !== null);

                const matchTemplateName =
                  !templateName || item.title?.toLowerCase().includes(templateName.toLowerCase());

                const matchModelNumber =
                  !modelNumber ||
                  item.asset_template?.modelNumber
                    ?.toLowerCase()
                    .includes(modelNumber.toLowerCase());

                return matchAssetType && matchGlobal && matchModelNumber && matchTemplateName;
              })}
              columns={columns}
              getRowId={(row) => row.id}
              slots={{
                toolbar: () => (
                  <QuickSearchToolbar
                    setShowFilter={setShowFilter}
                    filteredData={
                      <>
                        {appliedFilters.templateName && (
                          <Chip
                            label={`Template Name: ${appliedFilters.templateName}`}
                            onDelete={() => {
                              setAppliedFilters({ ...appliedFilters, templateName: '' });
                              setLocalTemplateName('');
                            }}
                            sx={{ mr: 1, mb: 1 }}
                          />
                        )}
                        {appliedFilters.modelNumber && (
                          <Chip
                            label={`Model Number: ${appliedFilters.modelNumber}`}
                            onDelete={() => {
                              setAppliedFilters({ ...appliedFilters, modelNumber: '' });
                              setLocalModelNumber('');
                            }}
                            sx={{ mr: 1, mb: 1 }}
                          />
                        )}
                        {appliedFilters.assetTypeId && (
                          <Chip
                            label={`Asset Type: ${getAssetTypeLabel(appliedFilters.assetTypeId)}`}
                            onDelete={() => {
                              setAppliedFilters({ ...appliedFilters, assetTypeId: '' });
                              setLocalAssetTypeId('');
                            }}
                            sx={{ mr: 1, mb: 1 }}
                          />
                        )}
                        {appliedFilters.isGlobal !== '' ? (
                          <Chip
                            sx={{ mr: 1, mb: 1 }}
                            label={`Global Template : ${appliedFilters.isGlobal}`}
                            onDelete={() => {
                              setLocalIsGlobal('');
                              setAppliedFilters({
                                ...appliedFilters,
                                isGlobal: '',
                              });
                            }}
                          />
                        ) : null}

                        {appliedFilters.templateName !== '' ||
                        appliedFilters.modelNumber !== '' ||
                        appliedFilters.assetTypeId !== '' ||
                        appliedFilters.isGlobal !== '' ? (
                          <Chip
                            label={'Clear all'}
                            sx={{
                              color: 'primary.main',
                              border: 'unset',
                              mr: 1,
                              mb: 1,
                            }}
                            onClick={() => {
                              setLocalAssetTypeId('');
                              setLocalIsGlobal('');
                              setLocalModelNumber('');
                              setLocalTemplateName('');
                              setAppliedFilters({
                                assetTypeId: '',
                                isGlobal: '',
                                modelNumber: '',
                                templateName: '',
                              });
                            }}
                          />
                        ) : null}
                      </>
                    }
                  />
                ),
                noRowsOverlay: CustomNoRowsOverlay,
              }}
              slotProps={{
                toolbar: {
                  showQuickFilter: true,
                  printOptions: { disableToolbarButton: true },
                  csvOptions: { disableToolbarButton: true },
                },
              }}
            />
          </Box>
        )}
        <Snackbar
          open={!!message}
          autoHideDuration={3000}
          onClose={() => setMessage(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert severity={message?.type}>{message?.text}</Alert>
        </Snackbar>
        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDashboardTemplate.open}
          onClose={() => setDeleteDashboardTemplate({ open: false, dashboardTemplateId: 0 })}
        >
          <DialogTitle>
            <Typography variant="h4">Delete Dashboard template?</Typography>
          </DialogTitle>
          <DialogContent>
            <Typography color="error">This action cannot be undone.</Typography>
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={() => setDeleteDashboardTemplate({ open: false, dashboardTemplateId: 0 })}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
              disabled={isLoading}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
      <Drawer
        anchor="right"
        open={showFilter}
        sx={{ width: 500 }}
        onClose={() => {
          setShowFilter(false);
        }}
      >
        <Box p={3} width={500}>
          <Typography variant="h6" gutterBottom>
            Filter Templates
          </Typography>

          <FormControl fullWidth margin="normal">
            <TextField
              label="Template Name"
              variant="outlined"
              value={localTemplateName}
              onChange={(e) => setLocalTemplateName(e.target.value)}
            />
          </FormControl>
          <FormControl fullWidth margin="normal">
            <TextField
              label="Model Number"
              variant="outlined"
              value={localModelNumber}
              onChange={(e) => setLocalModelNumber(e.target.value)}
            />
          </FormControl>
          <FormControl fullWidth margin="normal">
            <InputLabel id="asset-type-label">Asset Type</InputLabel>
            <Select
              labelId="asset-type-label"
              value={localAssetTypeId}
              label="Asset Type"
              onChange={(e: SelectChangeEvent) => setLocalAssetTypeId(e.target.value?.toString())}
            >
              {/* <MenuItem value="">All</MenuItem> */}
              {assetTypesWithPath?.map((assetType, index) => (
                <MenuItem value={assetType.value} key={index}>
                  {assetType.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth margin="normal">
            <InputLabel id="global-template-label">Global Template</InputLabel>
            <Select
              labelId="global-template-label"
              value={localIsGlobal}
              label="Global Template"
              onChange={(e: SelectChangeEvent) => setLocalIsGlobal(e.target.value)}
            >
              {/* <MenuItem value="">All</MenuItem> */}
              <MenuItem value="yes">Yes</MenuItem>
              <MenuItem value="no">No</MenuItem>
            </Select>
          </FormControl>

          <Box mt={2} gap={2} display="flex" justifyContent="space-between">
            <Button variant="contained" fullWidth onClick={handleApply}>
              Apply Filters
            </Button>
            <Button
              fullWidth
              variant="outlined"
              onClick={() => {
                setLocalAssetTypeId('');
                setLocalIsGlobal('');
                setLocalModelNumber('');
                setLocalAssetTypeId('');
                setAppliedFilters({
                  assetTypeId: '',
                  isGlobal: '',
                  modelNumber: '',
                  templateName: '',
                });
              }}
            >
              Clear Filters
            </Button>
          </Box>
        </Box>
      </Drawer>
    </Container>
  );
};

export default List;

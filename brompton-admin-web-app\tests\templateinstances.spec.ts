import { test, expect } from '@playwright/test';
test('templateinstance', async({page}) => {
    await page.goto('https://dev.pivotol.ai/login');
  
  // Login
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  
  await page.waitForTimeout(3000); // Reduce unnecessary delay

  // Click "Assets" button to expand menu
  await page.getByRole('button', { name: 'Assets' }).click();
  await page.waitForTimeout(3000); // Reduce unnecessary delay
  // Wait for "Manage Templates" menu item to appear and click
  const manageTemplates = page.getByRole('menuitem', { name: 'Manage Templates' });
  await manageTemplates.waitFor({ state: 'visible', timeout: 15000 });
  await manageTemplates.click();

  // Wait for the "Add Asset template" link inside the expanded menu
  await page.waitForTimeout(2000); // Ensure animation completes if needed

  const addAssetTemplateInstance = page.locator('a[href="/create-asset-template-instance"]');
await addAssetTemplateInstance.waitFor({ state: 'visible', timeout: 15000 });
await addAssetTemplateInstance.click();
await page.waitForTimeout(2000);

// Ensure dropdown options are visible
await page.getByLabel('Asset Type').click();
await page.getByRole('option', { name: 'Renewable > Battery Bank (32)' }).click();
await page.getByRole('row', { name: '11 TESLA Megapack1 Yes Export' }).getByRole('radio').check();
await page.getByRole('button', { name: 'Next', exact: true }).click();
await page.getByLabel('Unit Of Group *').click();
await page.getByRole('option', { name: 'us' }).click();
await page.getByLabel('Asset Tag *').click();
await page.getByLabel('Asset Tag *').fill('US');
await page.getByLabel('Select a time zone').click();
await page.getByRole('option', { name: 'Africa/Accra' }).click();
await page.getByLabel('Latitude').click();
await page.getByLabel('Latitude').fill('2');
await page.getByLabel('Longitude').click();
await page.getByLabel('Longitude').fill('1');
await page.getByLabel('Description').click();
await page.getByLabel('Description').fill('asset template instance');
await page.getByRole('button', { name: 'Next' }).click();
await page.getByRole('button', { name: 'Save & Finish' }).click();
await page.waitForTimeout(2000);
await page.close();
});
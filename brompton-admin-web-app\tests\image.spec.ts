import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://dev.pivotol.ai/login');

  // **Login**
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000);

  // **Navigate to Dashboard**
  await page.getByRole('button', { name: 'Dashboards' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage' }).click();
  await page.waitForTimeout(10000);

  //await page.getByRole('button', { name: 'Proceed' }).click();
  await page.getByRole('button', { name: 'New Dashboard' }).click();

  // Open widgets menu
  await page.locator('#widgets-icon').click();
  //await page.waitForTimeout(2000);

  // Drag and drop the table widget into the layout area
  await page.locator('#image').dragTo(page.locator('.react-grid-layout.layout'));
  //await page.waitForTimeout(2000);

  // Re-open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  // Hover over the parent element to reveal options
  const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
  await parentElement.hover();

  // Locate and click the 'MoreVertIcon' options button
  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  // Click on settings menu item
  await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
  await page.waitForTimeout(3000);

  // **Select Asset**
  await page.getByRole('combobox', { name: 'Select Asset', exact: true }).click();
  await page.getByRole('option', { name: 'BromptonSimulations > Camden Headquarter > BT-001 > BT-001-1' }).click();
  await page.getByRole('combobox', { name: 'Select Measurements' }).click();
  await page.getByRole('option', { name: 'Jt-' }).click();
  await page.getByRole('tab', { name: 'Look & feel' }).click();
  await page.getByRole('radio', { name: '/icons/leaf.svg' }).check();
  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForTimeout(5000);

  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: 'Last 90 days' }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.waitForTimeout(3000);
  // **Close Page**
  await page.close();
});

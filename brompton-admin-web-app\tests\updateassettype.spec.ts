import { test, expect } from '@playwright/test';
test('updateassettype', async ({ page }) => {
      // Open the URL
      await page.goto('https://dev.pivotol.ai/login');
  
      // Fill in Username and Password
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  
      // Click on Login Button
      await page.click('#__next > div > div > form > div > button');
      await page.waitForTimeout(3000);
  
      // Navigate to Asset Management
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.getByRole('menuitem', { name: 'Manage Types' }).click();
  
      // Go to next page 18 times to locate the row
      for (let i = 0; i < 18; i++) {
        await page.getByRole('button', { name: 'Go to next page' }).click();
      }
  
      // Locate the specific row and its edit icon
      const rowLocator = page.getByRole('row', { name: 'Test100 test100 Aircraft' });
      const editIcon = rowLocator.locator('[data-testid="ModeEditIcon"]');
  
      await editIcon.click();
      await page.waitForTimeout(3000);
  
      // Edit asset type
      await page.getByLabel('Asset Type Name').click();
      await page.getByLabel('Asset Type Name').fill('Test100');
      await page.getByLabel('Parent Type').click();
      await page.getByRole('option', { name: 'Location > Aircraft', exact: true }).click();
  
      // Submit changes
      await page.getByRole('button', { name: 'SAVE' }).click();
  
      await page.waitForTimeout(6000);
      await page.close();
    });
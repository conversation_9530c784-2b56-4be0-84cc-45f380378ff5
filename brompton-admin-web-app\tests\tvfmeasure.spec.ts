import { test, expect } from '@playwright/test';
test('create TimeVaryingFactor', async ({ page }) => {
    await page.goto('https://dev.pivotol.ai/login');

    // **Login**
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // **Navigate to Assets > Manage Assets**
    await page.getByRole('button', { name: 'Assets' }).click();
    await page.waitForTimeout(3000);
    await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
    await page.waitForTimeout(12000);

    // **Right-click on 'Animesh' tree item and ensure context menu appears**
    const treeItem = await page.getByRole('treeitem', { name: 'Demo' }).locator('div').nth(2);
    await treeItem.waitFor({ state: 'visible', timeout: 8000 });
    await treeItem.click({ button: 'right' });

    // **Click 'New Measurement'**
    const newMeasurementMenuItem = page.getByRole('menuitem', { name: 'New measurement' });
    await newMeasurementMenuItem.waitFor({ state: 'visible', timeout: 10000 });
    await newMeasurementMenuItem.click();
    await page.waitForTimeout(3000);

    // **Fill in the form**
    await page.getByLabel('Tag *').fill('createtvf');
    await page.getByLabel('Description').fill('test');

    // **Select Measurement Type**
    await page.getByLabel('Select measurement type *').click();
    await page.getByRole('option', { name: 'Acceleration' }).click();

    // **Select Data Type**
    await page.getByLabel('Select data type *').click();
    await page.getByRole('option', { name: 'BOOLEAN' }).click();

    // **Select Value Type**
    await page.getByLabel('Select value type *').click();
    await page.getByRole('option', { name: 'nominal' }).click();

    // **Select Unit of Measure**
    await page.getByLabel('Select unit of measure').click();
    await page.getByRole('option', { name: 'ft/s²' }).click();

    // **Select Location**
    await page.getByLabel('Select location').click();
    await page.getByRole('option', { name: 'EndOfLine' }).click();

    // **Select Data Source as "TimeVaryingFactor"**
    await page.getByLabel('Select datasource').click();
    await page.getByRole('option', { name: 'TimeVaryingFactor' }).click();

    // **Fill Meter Factor**
    await page.getByLabel('Meter factor').fill('6');

    // **Proceed to Next Step**
    await page.getByRole('button', { name: 'Next' }).click();

    // **Select Target Option**
    await page.getByRole('combobox').first().click();
    await page.getByRole('option', { name: 'Target' }).click();

    // **Add Effective Date**
    await page.getByRole('button', { name: 'Add Effective Date' }).click();
    await page.getByLabel('Choose date').click();
    await page.getByRole('gridcell', { name: '20' }).click();

    // **Select a Day**
    await page.getByRole('button', { name: 'Tuesday' }).click();
    await page.getByRole('option', { name: 'Friday' }).click();

    // **Choose Time**
    await page.getByLabel('Choose time').click();
    await page.getByLabel('4 hours').click();
    await page.getByLabel('20 minutes').click();
    await page.getByLabel('PM').click();

    // **Fill Additional Time Details**
    await page.getByRole('spinbutton').click();
    await page.getByRole('spinbutton').fill('07');

    // **Save Measurement**
    await page.getByRole('button', { name: 'Save' }).click();

    // **Wait and Close**
    await page.waitForTimeout(3000);
    await page.close();
  });
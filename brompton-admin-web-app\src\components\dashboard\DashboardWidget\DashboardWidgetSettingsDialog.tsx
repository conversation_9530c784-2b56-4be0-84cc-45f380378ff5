import {
  Autocomplete,
  Box,
  Card,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import DataWidgetSettingExceptionalSettings from '~/components/common/DataWidgetSettingsExceptional/DataWidgetSettingsExceptional';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import {
  dashboardTemplateApi,
  useGetDashboardTemplateDetailsQuery,
  useGetDashboardTemplatesQuery,
} from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getCurrentAssetType } from '~/redux/selectors/dashboardSelectors';
import { getCurrentSelectedAssetId } from '~/redux/selectors/treeSelectors';
import { getDesktopMobileMode } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { AssetTypeOption } from '~/types/asset';
import { DashboardState } from '~/types/dashboard';
import { DashboardTemplate } from '~/types/dashboardTemplate';
import { elementVariable } from '~/types/diagram';
import { DashboardWidget, ImageTextDetails, Widget } from '~/types/widgets';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type DashboardWidgetSettingsDialogProps = {
  settings: DashboardWidget;
  handleSettingsChange: (
    value: ((prevState: DashboardWidget) => DashboardWidget) | DashboardWidget,
  ) => void;
};

type AssetOption = {
  label: string;
  id: number;
};

const DashboardWidgetSettingsDialog = ({
  settings,
  handleSettingsChange,
}: DashboardWidgetSettingsDialogProps) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [assetType, setAssetType] = useState<number>(0);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const customerId = useSelector(getCustomerId);
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const [supportedMetrics, setSupportedMetrics] = useState<number[]>([]);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [filteredAssetMeasurements, setFilteredAssetMeasurements] = useState<
    { key: number; metricName: string; measurements: string }[]
  >([]);
  const currentMode = useSelector(getDesktopMobileMode);
  const [assetMeasurementsListMap, setAssetMeasurementsListMap] = useState<
    Map<
      number,
      {
        id: number;
        tag: string;
      }[]
    >
  >(new Map());

  const {
    data: dashboardTemplates,
    isFetching: fetchingTemplates,
    isLoading: loadingDashboardTemplates,
    isError: fetchingDashboardTemplateserror,
  } = useGetDashboardTemplatesQuery(
    { assetTypeId: assetTypeTemplate || assetType },
    {
      skip: !(assetTypeTemplate || assetType),
      refetchOnMountOrArgChange: true,
    },
  );

  const { isLoading: isLoadingDashboardTemplates, data: templateData } =
    useGetDashboardTemplateDetailsQuery(
      settings.dashboardTemplateOption ? Number(settings.dashboardTemplateOption.id) : 0,
      {
        skip:
          !settings.dashboardTemplateOption || Number(settings.dashboardTemplateOption.id) === 0,
        refetchOnMountOrArgChange: true,
      },
    );

  const {
    data: assetMeasurements,
    isFetching: isMeasurementsFetching,
    refetch: refetchMeasurement,
    isUninitialized,
  } = useGetAllMeasurementsQuery(
    {
      assetId: Number(settings.assetOption?.id),
      customerId: customerId,
    },
    {
      skip: !settings.assetOption?.id || !customerId || String(settings.assetOption?.id) === '',
      refetchOnMountOrArgChange: true,
    },
  );

  const {
    data: assetData,
    isLoading: isAssetLoading,
    isFetching: isAssetReloading,
    error: assetError,
  } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const { data: assetTypeListData, isSuccess: isSuccessfullBackOffieAssetTypes } =
    useGetAllBackOfficeAssetTypesQuery(undefined, {
      skip: settings.mode === 'dashboard' || !assetTypeTemplate || assetTypeTemplate <= 0,
    });

  const assetTypesWithPathForAsset = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const getFilteredAssetMeasurements = async (
    assetMeasurementsListMap: Map<number, { id: number; tag: string }[]>,
    supportedMetrics: number[],
    templateData: DashboardTemplate,
    isTemplateMode: boolean,
    currentMode: number,
  ) => {
    const pushMetricMapping = (
      extracted: any[],
      metricId: number,
      metricName: string,
      measures: { id: number; tag: string }[] | undefined,
    ) => {
      if (!metricId || !measures || measures.length === 0) return;
      if (extracted.find((e) => e.key === metricId)) return;
      extracted.push({
        key: metricId,
        metricName,
        measurements: measures.map((m) => `${m.id}-${m.tag}`).join(', '),
      });
    };

    // 1. Template mode
    if (isTemplateMode && templateData?.asset_template?.measurements) {
      return templateData.asset_template.measurements
        .filter((m: any) => supportedMetrics.includes(m.metric.id))
        .map((m: any) => ({
          key: m.metric.id,
          metricName: m.metric.name,
          measurements: '-', // no mapping shown in template mode
        }));
    }
    // 2. Dashboard-widget mode
    if (templateData?.data) {
      try {
        const parsed = JSON.parse(templateData.data);
        const widgets =
          parsed?.responsiveLayouts?.[currentMode === 0 ? 'desktop' : 'mobile']?.widgets || [];
        const extracted: {
          key: number;
          metricName: string;
          measurements: string;
        }[] = [];

        const templateMeasurements = templateData.asset_template?.measurements || [];

        for (const widget of widgets) {
          if (widget.type === 'dashboard-widget') {
            const setting = widget.settings as DashboardWidget;
            const { data: templateData, isError } = await dispatch(
              dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(
                setting.dashboardTemplateOption.id,
              ),
            );
            if (isError || !templateData) {
              return [];
            }

            const results = await getFilteredAssetMeasurements(
              assetMeasurementsListMap,
              supportedMetrics,
              templateData,
              isTemplateMode,
              currentMode,
            );
            results.forEach((r) => {
              if (!extracted.find((e) => e.key === r.key)) {
                extracted.push(r);
              }
            });
          }
          const innerWidgets = [widget];
          for (const inner of innerWidgets) {
            const settings = inner.settings || inner; // fallback for flat widgets
            const widgetType = inner.type as Widget['type'];

            const addMetric = (metricId: number) => {
              const metricInfo = templateMeasurements.find((m: any) => m.metric.id === metricId);
              const metricName = metricInfo?.metric.name ?? 'N/A';
              const measures = assetMeasurementsListMap.get(metricId);

              if (metricId && assetMeasurementsListMap.has(metricId) && measures?.length) {
                pushMetricMapping(extracted, metricId, metricName, measures);
              }
            };

            switch (widgetType) {
              case 'stats':
              case 'title':
              case 'kpi-bar-chart':
              case 'kpi-percentage':
              case 'kpi-color-box':
              case 'image-stats':
              case 'kpi-sparkline':
              case 'kpi-value-indicator':
                if (settings.selectedDbMeasureId) {
                  addMetric(Number(settings.selectedDbMeasureId));
                }
                break;

              case 'table':
              case 'kpi-table':
              case 'image':
              case 'alert-widget':
                (settings.selectedTitles || []).forEach((id: number | string) => {
                  addMetric(Number(id));
                });
                break;

              case 'chart':
                (settings.settings?.selectedTitles || []).forEach((id: number | string) => {
                  addMetric(Number(id));
                });

                if (settings.settings?.selectedSparkMeasure?.measureId) {
                  addMetric(Number(settings.settings.selectedSparkMeasure.measureId));
                }
                break;

              case 'map':
                (settings.markers || []).forEach((marker: any) => {
                  (marker.selectedTitles || []).forEach((id: number | string) => {
                    addMetric(Number(id));
                  });
                });
                break;

              case 'Diagram':
                Object.values(settings.elementIdVariabels || {})
                  .flat()
                  .forEach((v: any) => {
                    if (v.measurementId) {
                      addMetric(Number(v.measurementId));
                    }
                  });
                (settings.elementVariable || []).forEach((v: any) => {
                  if (v.measurementId) {
                    addMetric(Number(v.measurementId));
                  }
                });
                break;

              // case 'chart':
              //   if (settings.selectedDbMeasureId) {
              //     addMetric(Number(settings.selectedDbMeasureId));
              //   }
              //   break;
              default:
                break;
            }
          }
        }

        if (extracted.length > 0) {
          return extracted.filter(
            (item, index, self) => index === self.findIndex((t) => t.key === item.key),
          );
        }
      } catch (err) {}
    }

    // 3. Default dashboard mode fallback
    return Array.from(assetMeasurementsListMap.keys())
      .filter((key) => supportedMetrics.includes(key))
      .map((key) => ({
        key,
        metricName:
          templateData.asset_template?.measurements?.find(
            (measure: any) => measure.metric.id === key,
          )?.metric.name ?? 'N/A',
        measurements:
          assetMeasurementsListMap
            .get(key)
            ?.map((measure) => `${measure.id}-${measure.tag}`)
            .join(', ') || 'N/A',
      }));
  };

  useEffect(() => {
    if (assetTypeTemplate && assetData && assetData.length > 0) {
      setAssetType(assetTypeTemplate);

      // Auto-select the first asset matching the assetTypeTemplate
      const defaultAsset = assetData.find((a) => a.assetTypeId === assetTypeTemplate);
      if (defaultAsset) {
        handleSettingsChange({
          ...settings,
          assetOption: {
            id: defaultAsset.id,
            label: defaultAsset.tag || `Asset ${defaultAsset.id}`,
          },
          assetId: String(defaultAsset.id),
        });
      }
    }
  }, [assetTypeTemplate, assetData]);
  useEffect(() => {
    const isAssetUnset = settings.assetOrAssetType === null;
    const isAssetTemplateValid = assetTypeTemplate !== undefined && assetTypeTemplate !== null;

    if (settings.mode === 'template' && isAssetUnset && isAssetTemplateValid) {
      handleSettingsChange({
        ...settings,
        assetOrAssetType: assetTypeTemplate,
      });
    }
  }, [settings.mode, assetTypeTemplate, assetData, settings.assetOrAssetType]);

  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);

  useEffect(() => {
    if (assetData && settings.assetOption?.id) {
      const asset = assetData.find((a) => a.id === settings.assetOption?.id);
      if (asset) {
        setAssetType(asset.assetTypeId);
      } else {
        setAssetType(0);
      }
    }
  }, [assetData, settings.assetOption]);

  useEffect(() => {
    const fetchFilteredMeasurements = async () => {
      if (assetMeasurementsListMap && supportedMetrics && templateData) {
        const isTemplateMode = settings.mode === 'template';
        const filtered = await getFilteredAssetMeasurements(
          assetMeasurementsListMap,
          supportedMetrics,
          templateData,
          isTemplateMode,
          currentMode,
        );
        setFilteredAssetMeasurements(filtered);
      }
    };

    fetchFilteredMeasurements();
  }, [assetMeasurementsListMap, supportedMetrics, templateData, currentMode]);

  useEffect(() => {
    if (isMeasurementsFetching === true || assetId) {
      setAssetMeasurementsListMap(new Map());
    }
    if (assetMeasurements && isMeasurementsFetching === false) {
      const assetMeasurementsMap = new Map<
        number,
        {
          id: number;
          tag: string;
        }[]
      >(new Map());
      assetMeasurements?.forEach((item) => {
        if (item.metric_id) {
          if (assetMeasurementsMap.has(item.metric_id)) {
            assetMeasurementsMap.set(item.metric_id, [
              ...(assetMeasurementsMap.get(item.metric_id) ?? []),
              {
                id: item.id,
                tag: item.tag,
              },
            ]);
          } else {
            assetMeasurementsMap.set(item.metric_id, [
              {
                id: item.id,
                tag: item.tag,
              },
            ]);
          }
        }
      });
      setAssetMeasurementsListMap(assetMeasurementsMap);
    }
  }, [assetMeasurements, isMeasurementsFetching, assetId]);

  useEffect(() => {
    if (templateData && templateData.data) {
      const templateDetailsData = JSON.parse(templateData.data) as {
        widget: DashboardState['widget'];
        topPanel: DashboardState['template']['topPanel'];
        chart: DashboardState['template']['chart'];
      };

      const metricsToShow: number[] = [];
      const metricMeasurements: Record<string, { metricName: string; measurement: string }> = {};
      const widgets = templateDetailsData.widget.widgets.map((widget) => {
        switch (widget.type) {
          case 'title': {
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';

            // Handle updated TitleWidget type
            if (
              widget.settings.valueMode === 'measurement' &&
              widget.settings.selectedDbMeasureId
            ) {
              const measures = assetMeasurementsListMap.get(
                Number(widget.settings.selectedDbMeasureId),
              );

              if (measures && measures.length > 0) {
                widget.settings.measurementId = measures[0].id;
                widget.settings.assetId = Number(settings.assetOption.id);
                widget.settings.selectedDbMeasureId = measures[0].id.toString();
              } else {
                widget.settings.measurementId = undefined;
                widget.settings.assetId = Number(settings.assetOption.id);
                widget.settings.selectedDbMeasureId = '';
              }
            } else if (widget.settings.valueMode === 'fixed') {
              widget.settings.measurementId = undefined;
              widget.settings.assetId = Number(settings.assetOption.id);
              widget.settings.selectedDbMeasureId = '';
            }
            return widget;
          }
          case 'Weather': {
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'stats': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.mode = 'dashboard';
            widget.settings.isChildWidget = true;
            metricMeasurements[widget.settings.selectedDbMeasureId] = {
              metricName: widget.settings.selectedDbMeasureId,
              measurement: measures ? measures[0].tag : 'N/A',
            };
            return widget;
          }
          case 'map': {
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            widget.settings.markers = widget.settings.markers.map((marker) => {
              if (marker.selectedTitles) {
                marker.selectedTitles.forEach((title) => {
                  if (!metricsToShow.includes(Number(title))) {
                    metricsToShow.push(Number(title));
                  }
                });
                const measures = marker.selectedTitles.flatMap((title) => {
                  const measureList = assetMeasurementsListMap.get(Number(title));
                  return measureList
                    ? measureList.map((measure) => ({
                        assetId: settings.assetOption.id.toString(),
                        measureId: measure.id.toString(),
                      }))
                    : [];
                });
                marker.assetMeasures = measures.length > 0 ? measures : [];
                marker.labelAndUnits = marker.selectedTitles.reduce((acc, title) => {
                  const measureList = assetMeasurementsListMap.get(Number(title));
                  if (measureList) {
                    measureList.forEach((measure) => {
                      acc[measure.id.toString()] = {
                        label: measure.tag,
                        unit: marker.labelAndUnits[measure.id.toString()]?.unit || '',
                        value: '',
                      };
                    });
                  }
                  return acc;
                }, {} as Record<string, { label: string; unit: string; value: string }>);
                marker.selectedTitles = measures.map((measure) => measure.measureId);
              } else {
                marker.assetMeasures = [];
                marker.labelAndUnits = {};
              }
              return marker;
            });
            return widget;
          }
          case 'table': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            return widget;
          }
          case 'alert-widget': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'chart': {
            const chartType = widget.settings.chartType;
            widget.settings.settings.isChildWidget = true;
            widget.settings.settings.mode = 'dashboard';
            if (chartType === 'scatter' || chartType === 'bar') {
              widget.settings.settings.selectedTitles.map((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });
              const measures = widget.settings.settings.selectedTitles
                .map((title) => {
                  const measurements: number[] = [];
                  if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                    const measureIds =
                      assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                    measurements.push(...measureIds);
                  }
                  return measurements;
                })
                .flat();
              widget.settings.settings.selectedTitles = measures.map((measure) =>
                measure.toString(),
              );
              const titles: number[] = [];
              if (widget.settings.settings.overrideGlobalBarColor) {
                widget.settings.settings.barColors = widget.settings.settings.barColors.map(
                  (bar) => {
                    const { color, measureId } = bar;
                    const measurement = assetMeasurementsListMap.get(Number(measureId));
                    const measureToMap: { id: number | null } = { id: null }; // Changed to `const`
                    if (measurement && measurement.length > 0) {
                      for (const measure of measurement) {
                        if (measures.includes(measure.id) && !titles.includes(measure.id)) {
                          titles.push(measure.id);
                          measureToMap.id = measure.id;
                        }
                      }
                    }
                    return {
                      color,
                      measureId: measureToMap.id?.toString() ?? '', // Use the valid id or fallback to an empty string
                    };
                  },
                );
              }
              widget.settings.settings.dbMeasureIdToName = Array.from(
                assetMeasurementsListMap.entries(),
              ).reduce((acc: Record<string, string>, [_, measurements]) => {
                measurements.forEach((measurement) => {
                  acc[measurement.id.toString()] = measurement.tag;
                });
                return acc;
              }, {});
              if (
                chartType === 'scatter' &&
                widget.settings.settings.showStacked &&
                widget.settings.settings.selectedSparkMeasure?.measureId &&
                widget.settings.settings.selectedSparkMeasure?.measureId !== ''
              ) {
                if (
                  widget.settings.settings.showStacked &&
                  widget.settings.settings.selectedSparkMeasure?.measureId &&
                  widget.settings.settings.selectedSparkMeasure?.measureId !== ''
                ) {
                  if (
                    !metricsToShow.includes(
                      Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                    )
                  ) {
                    metricsToShow.push(
                      Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                    );
                  }
                  const measures = assetMeasurementsListMap.get(
                    Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                  );
                  widget.settings.settings.selectedSparkMeasure.assetId = assetId.toString();
                  widget.settings.settings.selectedSparkMeasure.measureId =
                    measures?.[0].id.toString() ?? '';
                } else {
                  widget.settings.settings.showSparkLine = false;
                  widget.settings.settings.selectedSparkMeasure.measureId = '';
                  widget.settings.settings.selectedSparkMeasure.assetId = '';
                }
              }
              widget.settings.settings.assetMeasure = [
                {
                  assetId: settings.assetOption.id.toString(),
                  measureId: measures.map((measure) => measure.toString()),
                },
              ];
              return widget;
            }
            if (chartType === 'bullet' || chartType === 'indicator' || chartType === 'heatmap') {
              if (!metricsToShow.includes(Number(widget.settings.settings.selectedDbMeasureId))) {
                metricsToShow.push(Number(widget.settings.settings.selectedDbMeasureId));
              }
              const measures =
                assetMeasurementsListMap.get(
                  Number(widget.settings.settings.selectedDbMeasureId),
                ) !== undefined &&
                assetMeasurementsListMap.get(Number(widget.settings.settings.selectedDbMeasureId));
              if (measures) {
                widget.settings.settings.selectedDbMeasureId = measures[0].id.toString();
                widget.settings.settings.assetMeasure = {
                  assetId: settings.assetOption.id.toString(),
                  measureId: measures.map((measure) => measure.id.toString()),
                };
              } else {
                widget.settings.settings.selectedDbMeasureId = '';
                widget.settings.settings.assetMeasure = {
                  assetId: settings.assetOption.id.toString(),
                  measureId: [],
                };
              }
            }
            if (chartType === 'sankey') {
              const metrics = widget.settings.settings.Label.filter(
                (metric) => metric.sourceFrom === 'Default',
              ).map((metric) => metric.sourceName);
              metrics.map((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });
              widget.settings.settings.Label = widget.settings.settings.Label.map((label) => {
                if (label.sourceFrom === 'Default') {
                  let measureToMap: {
                    id: number | null;
                    tag: string | null;
                  } = {
                    id: null,
                    tag: null,
                  };
                  const measure =
                    assetMeasurementsListMap.get(Number(label.sourceAssetMeasure?.measureId)) ??
                    assetMeasurementsListMap.get(Number(label.sourceName));

                  if (measure && measure.length > 0) {
                    const selectedMeasure = measure.at(0);
                    measureToMap = {
                      id: selectedMeasure?.id ?? 0,
                      tag: selectedMeasure?.tag.toString() ?? '',
                    };
                  }
                  return {
                    ...label,
                    sourceLabel: formatMetricLabel(measureToMap?.tag ?? ''),
                    sourceName: measureToMap?.id?.toString() ?? '',
                    sourceAssetMeasure: {
                      ...label.sourceAssetMeasure,
                      assetId: settings.assetOption.id.toString(),
                      measureId: measureToMap?.id?.toString() ?? '',
                    },
                  };
                }
                return label;
              });
            }
            return widget;
          }
          case 'kpi-bar-chart': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            const dbMeasureIdToName: Record<string, string> = {};
            assetMeasurementsListMap.forEach((measures) => {
              measures.forEach((measure) => {
                if (widget.settings.selectedDbMeasureId === measure.id.toString()) {
                  dbMeasureIdToName[measure.id] = measure.tag;
                }
              });
            });
            widget.settings.dbMeasureIdToName = dbMeasureIdToName;
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'kpi-percentage': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'kpi-table': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            return widget;
          }
          case 'kpi-color-box': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'image-stats':
          case 'kpi-value-indicator':
          case 'kpi-sparkline': {
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'Diagram': {
            Object.keys(widget.settings.elementIdVariabels).map((ids) => {
              const variables = widget.settings.elementIdVariabels[ids];
              return variables.map((vars) => {
                const title = vars.measurementId;
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
                return {
                  measurementId: vars.measurementId, // Returning the measurementId for each variable
                };
              });
            });

            widget.settings.elementIdVariabels = Object.keys(
              widget.settings.elementIdVariabels,
            ).reduce(
              (acc, ids) => {
                const variables = widget.settings.elementIdVariabels[ids];

                // Assign measurementId to each variable and push to the accumulator
                const updatedVariables = variables.map((vars) => {
                  const measures = assetMeasurementsListMap.get(Number(vars.measurementId));
                  const measurementId = measures ? measures[0].id.toString() : ''; // Take the first measure's id
                  return {
                    ...vars, // Spread the original variable properties
                    assetId: settings.assetOption.id.toString(),
                    measurementId: measurementId, // Add the measurementId to each variable
                  };
                });

                // Accumulate the updated variables under the corresponding ids
                acc[ids] = updatedVariables;
                return acc;
              },
              {} as Record<string, elementVariable[]>, // Initialize the accumulator with the correct type
            );
            widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
              const measures = assetMeasurementsListMap.get(Number(variable.measurementId));
              const measurementId = measures ? measures[0].id.toString() : '';
              return {
                ...variable,
                assetId: settings.assetOption.id.toString(),
                measurementId: measurementId,
              };
            });
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'image': {
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });

            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];

            const measureIdImageText: Record<string, ImageTextDetails> = {};
            Array.from(assetMeasurementsListMap.keys()).forEach((metric) => {
              const imageToText = widget.settings.measureIdToImageTextDetails[metric.toString()];
              if (imageToText) {
                const measurement = assetMeasurementsListMap.get(metric)?.map((measures) => {
                  return measures;
                });
                measurement?.forEach((measure) => {
                  measureIdImageText[measure.id.toString()] = {
                    ...imageToText,
                    id: measure.id.toString(),
                    label: measure.tag,
                  };
                });
              }
            });
            const dbMeasureIdToName: Record<string, string> = {};
            assetMeasurementsListMap.forEach((measures) => {
              measures.forEach((measure) => {
                dbMeasureIdToName[measure.id] = measure.tag;
              });
            });
            widget.settings.dbMeasureIdToName = dbMeasureIdToName;
            widget.settings.measureIdToImageTextDetails = measureIdImageText;
            return widget;
          }

          case 'multi-plot': {
            const allSelectedTitles: string[] = [];

            widget.settings.subplots = widget.settings.subplots.map((subplot) => {
              const updatedAssetMeasures = subplot.assetMeasures.map((assetMeasure) => {
                if (!assetMeasure.selectedDbMeasureId) {
                  return assetMeasure;
                }

                const selectedDbId = Number(assetMeasure.selectedDbMeasureId);

                if (!metricsToShow.includes(selectedDbId)) {
                  metricsToShow.push(selectedDbId);
                }

                const measures = assetMeasurementsListMap.get(selectedDbId);

                if (measures && measures.length > 0) {
                  allSelectedTitles.push(...measures.map((m) => m.id.toString()));

                  return {
                    ...assetMeasure,
                    assetId: settings.assetOption.id.toString(),
                    measureId: measures.map((m) => m.id.toString()),
                  };
                } else {
                  return {
                    ...assetMeasure,
                    assetId: settings.assetOption.id.toString(),
                    measureId: [],
                  };
                }
              });

              return {
                ...subplot,
                assetMeasures: updatedAssetMeasures,
              };
            });

            widget.settings.dbMeasureIdToName = Array.from(
              assetMeasurementsListMap.entries(),
            ).reduce((acc: Record<string, string>, [_, measures]) => {
              measures.forEach((measure) => {
                acc[measure.id.toString()] = measure.tag;
              });
              return acc;
            }, {});

            widget.settings.selectedTitles = allSelectedTitles;

            widget.settings.mode = 'dashboard';
            widget.settings.isChildWidget = true;
            return widget;
          }
        }
        return widget;
      });
      handleSettingsChange({
        ...settings,
        dashboardTemplateData: {
          widgets,
          widgetLayout: templateDetailsData.widget.widgetLayout,
          lastWidgetId: templateDetailsData.widget.lastWidgetId,
          deleteWidgets: templateDetailsData.widget.deleteWidgets,
        },
      });
      dispatch(dashboardSlice.actions.setMetricMeasurements(metricMeasurements));
      setSupportedMetrics(metricsToShow);
    }
  }, [templateData]);

  const handleAssetChange = (event: any, newValue: AssetOption | null) => {
    handleSettingsChange({
      ...settings,
      assetId: newValue ? String(newValue.id) : '',
      assetOption: {
        id: newValue ? newValue.id : 0,
        label: newValue ? newValue.label : '',
      },
      dashboardTemplateOption: {
        id: 0,
        label: '',
      },
    });
  };
  const handleChangeDashboardTemplate = (templateId: number, templateName: string) => {
    handleSettingsChange({
      ...settings,
      dashboardTemplateOption: {
        id: templateId,
        label: templateName,
      },
    });

    // ✅ Only refetch if a valid asset is selected and query was initialized
    if (settings.assetOption?.id && !isUninitialized) {
      refetchMeasurement();
    }
  };

  return (
    <DataWidgetSettingExceptionalSettings
      settings={settings}
      setSettings={(value: any) => {
        handleSettingsChange(value);
      }}
      dataTabChildren={
        <Box component={Card} p={2} mt={2} mb={2}>
          <>
            {settings.mode === 'dashboard' ? (
              <>
                <Box sx={{ width: '100%', mt: 1 }}>
                  <Autocomplete
                    fullWidth
                    id={`asset-autocomplete`}
                    loading={isAssetReloading}
                    options={
                      !isAssetReloading
                        ? assetTypesWithPathForAsset.map((asset) => ({
                            label: asset.label,
                            id: asset.id,
                          }))
                        : []
                    }
                    getOptionLabel={(option) => option.label}
                    onChange={handleAssetChange}
                    value={settings.assetOption}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Select Asset"
                        variant="outlined"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {isAssetLoading ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Box>
                <Box sx={{ display: 'flex', mt: 2 }}>
                  <Autocomplete
                    fullWidth
                    disablePortal
                    id="combo-box-demo"
                    disabled={loadingDashboardTemplates || fetchingTemplates}
                    options={
                      settings.assetOption?.id
                        ? dashboardTemplates?.items?.map((item) => {
                            return {
                              id: item.id.toString(),
                              label: item.title,
                            };
                          }) ?? []
                        : []
                    }
                    value={{
                      id: settings.dashboardTemplateOption?.id?.toString() ?? '',
                      label: settings.dashboardTemplateOption?.label ?? '',
                    }}
                    onChange={(event, value) => {
                      handleChangeDashboardTemplate(Number(value?.id) ?? 0, value?.label ?? '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        error={fetchingDashboardTemplateserror}
                        label="Dashboard Template"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingDashboardTemplates ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Box>
              </>
            ) : (
              <>
                <Box sx={{ width: '100%', mt: 1 }}>
                  <Autocomplete
                    fullWidth
                    loading={isAssetReloading}
                    id="asset-type-autocomplete"
                    options={assetTypesWithPath.map((item) => ({
                      id: item.value.toString(),
                      label: item.label,
                    }))}
                    value={
                      assetTypeTemplate
                        ? {
                            id: assetTypeTemplate.toString(),
                            label:
                              assetTypesWithPath.find((item) => item.value === assetTypeTemplate)
                                ?.label ?? '',
                          }
                        : assetTypesWithPath
                            .map((item) => ({
                              id: item.value.toString(),
                              label: item.label,
                            }))
                            .find((item) => item.id === settings.assetOrAssetType?.toString()) ??
                          null
                    }
                    disabled={!!assetTypeTemplate}
                    onChange={(event, value) => {
                      if (!assetTypeTemplate) {
                        setAssetType(Number(value?.id));
                        handleSettingsChange({
                          ...settings,
                          assetOrAssetType: Number(value?.id) ?? null,
                        });
                      }
                    }}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    renderInput={(params) => <TextField {...params} label="Asset Type" />}
                  />
                </Box>
                <Box sx={{ display: 'flex', mt: 2 }}>
                  <Autocomplete
                    fullWidth
                    disablePortal
                    id="combo-box-demo"
                    disabled={loadingDashboardTemplates || fetchingTemplates}
                    options={
                      settings.assetOrAssetType !== null && settings.assetOrAssetType !== undefined
                        ? dashboardTemplates?.items?.map((item) => {
                            return {
                              id: item.id.toString(),
                              label: item.title,
                            };
                          }) ?? []
                        : []
                    }
                    value={{
                      id: settings.dashboardTemplateOption?.id?.toString() ?? '',
                      label: settings.dashboardTemplateOption?.label ?? '',
                    }}
                    onChange={(event, value) => {
                      handleChangeDashboardTemplate(Number(value?.id) ?? 0, value?.label ?? '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        error={fetchingDashboardTemplateserror}
                        label="Dashboard Template"
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {loadingDashboardTemplates ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                        }}
                      />
                    )}
                  />
                </Box>
              </>
            )}

            <Box mt={2}>
              {isLoadingDashboardTemplates ? (
                <CircularProgress size={20} />
              ) : templateData && settings.dashboardTemplateOption.id ? (
                <>
                  {isMeasurementsFetching ? (
                    <CircularProgress size={20} />
                  ) : (
                    <Typography variant="h5" mb={2}>
                      Measure Mapping:
                    </Typography>
                  )}

                  {!isMeasurementsFetching && filteredAssetMeasurements.length === 0 && (
                    <Typography>No measurements found</Typography>
                  )}

                  {filteredAssetMeasurements.length > 0 && (
                    <Box mt={2}>
                      <TableContainer component={Paper}>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Metric Name</TableCell>
                              <TableCell>Measurements</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {filteredAssetMeasurements.map(
                              ({
                                key,
                                metricName,
                                measurements,
                              }: {
                                key: number;
                                metricName: string;
                                measurements: string;
                              }) => (
                                <TableRow key={key}>
                                  <TableCell>{metricName}</TableCell>
                                  <TableCell>{measurements}</TableCell>
                                </TableRow>
                              ),
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Box>
                  )}
                </>
              ) : null}
            </Box>
          </>
        </Box>
      }
    />
  );
};

export default DashboardWidgetSettingsDialog;
